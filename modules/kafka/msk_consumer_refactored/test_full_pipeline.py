#!/usr/bin/env python3
"""
Comprehensive test script to simulate the full MSK consumer pipeline:
1. Load sample data (pyhub_sample.txt)
2. Process Avro union types
3. Convert timestamps and decimals
4. Validate fields against target table schema
5. Simulate PostgreSQL insertion with proper data adaptation
6. Test error handling scenarios

This script helps identify potential issues before deploying to production.
"""

import json
import sys
import os
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def load_sample_data(filename: str = 'pyhub_sample.txt') -> Dict:
    """Load sample data from file."""
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Sample file {filename} not found")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing JSON: {e}")
        return {}

def simulate_avro_processing(record: Dict) -> Dict:
    """Simulate the Avro union type processing."""
    print("🔄 Step 1: Processing Avro union types...")
    processed = {}
    
    for field_name, value in record.items():
        if isinstance(value, dict):
            if 'bytes' in value:
                try:
                    processed[field_name] = Decimal(str(value['bytes']))
                    print(f"  ✅ Decimal: {field_name} = {processed[field_name]}")
                except:
                    processed[field_name] = value['bytes']
                    print(f"  ⚠️  Decimal conversion failed: {field_name} = {processed[field_name]}")
            elif 'string' in value:
                processed[field_name] = value['string']
                print(f"  ✅ String: {field_name} = '{processed[field_name]}'")
            elif 'long' in value:
                processed[field_name] = value['long']
                print(f"  ✅ Long: {field_name} = {processed[field_name]}")
            elif 'int' in value:
                processed[field_name] = value['int']
                print(f"  ✅ Int: {field_name} = {processed[field_name]}")
            else:
                processed[field_name] = value
                print(f"  ⚠️  Unknown union: {field_name} = {value}")
        else:
            processed[field_name] = value
    
    return processed

def simulate_timestamp_conversion(record: Dict, timestamp_columns: List[str]) -> Dict:
    """Simulate timestamp conversion with bounds checking."""
    print("🔄 Step 2: Converting timestamps...")
    
    for field_name, value in record.items():
        if field_name.lower() in [col.lower() for col in timestamp_columns]:
            if isinstance(value, int):
                try:
                    if value > 1000000000000000:  # Microseconds
                        if value <= 253402300799999999:
                            converted = datetime.fromtimestamp(value / 1000000).strftime('%Y-%m-%d %H:%M:%S')
                            record[field_name] = converted
                            print(f"  ✅ Microsecond timestamp: {field_name} = {converted}")
                        else:
                            print(f"  ⚠️  Timestamp too large (microseconds): {field_name} = {value}")
                    elif value > 1000000000000:  # Milliseconds
                        if value <= 253402300799999:
                            converted = datetime.fromtimestamp(value / 1000).strftime('%Y-%m-%d %H:%M:%S')
                            record[field_name] = converted
                            print(f"  ✅ Millisecond timestamp: {field_name} = {converted}")
                        else:
                            print(f"  ⚠️  Timestamp too large (milliseconds): {field_name} = {value}")
                    elif value >= 946684800:  # Seconds
                        if value <= 253402300799:
                            converted = datetime.fromtimestamp(value).strftime('%Y-%m-%d %H:%M:%S')
                            record[field_name] = converted
                            print(f"  ✅ Second timestamp: {field_name} = {converted}")
                        else:
                            print(f"  ⚠️  Timestamp too large (seconds): {field_name} = {value}")
                    else:
                        print(f"  ⚠️  Timestamp too small: {field_name} = {value}")
                except Exception as e:
                    print(f"  ❌ Timestamp conversion error: {field_name} = {value}, error: {e}")
    
    return record

def simulate_field_validation(record: Dict, table_columns: List[str]) -> Dict:
    """Simulate field validation against target table schema."""
    print("🔄 Step 3: Validating fields against table schema...")
    
    # Exclude metadata fields
    record_fields = set(record.keys()) - {'_kafka_offset', '_raw_message'}
    table_columns_set = set(table_columns)
    
    extra_fields = record_fields - table_columns_set
    missing_fields = table_columns_set - record_fields
    matching_fields = record_fields & table_columns_set
    
    print(f"  📊 Field Analysis:")
    print(f"     Matching fields: {len(matching_fields)}")
    print(f"     Extra fields: {len(extra_fields)}")
    print(f"     Missing fields: {len(missing_fields)}")
    
    if extra_fields:
        print(f"  ⚠️  Extra fields (will be ignored): {sorted(list(extra_fields))[:5]}{'...' if len(extra_fields) > 5 else ''}")
    
    if missing_fields:
        print(f"  ⚠️  Missing fields (will be NULL): {sorted(list(missing_fields))[:5]}{'...' if len(missing_fields) > 5 else ''}")
    
    return {
        'valid': True,
        'extra_fields': list(extra_fields),
        'missing_fields': list(missing_fields),
        'matching_fields': list(matching_fields)
    }

def simulate_postgres_adaptation(record: Dict, table_columns: List[str]) -> List[Any]:
    """Simulate PostgreSQL value adaptation."""
    print("🔄 Step 4: Adapting values for PostgreSQL...")
    
    adapted_values = []
    errors = []
    
    for col in table_columns:
        if col in record:
            value = record[col]
            try:
                # Simulate adapt_value_for_postgres logic
                if value is None:
                    adapted_values.append(None)
                elif isinstance(value, Decimal):
                    adapted_values.append(float(value))  # PostgreSQL adapter converts Decimal to float
                elif isinstance(value, dict):
                    adapted_values.append(json.dumps(value))  # JSON serialization
                elif isinstance(value, (list, tuple)):
                    adapted_values.append(json.dumps(value))  # JSON serialization
                elif isinstance(value, bool):
                    adapted_values.append(value)
                elif isinstance(value, (int, float, str)):
                    adapted_values.append(value)
                else:
                    adapted_values.append(str(value))  # Convert to string as fallback
                
                print(f"  ✅ Adapted {col}: {type(value).__name__} -> {type(adapted_values[-1]).__name__}")
            except Exception as e:
                errors.append(f"Failed to adapt {col}: {e}")
                adapted_values.append(None)
                print(f"  ❌ Adaptation error {col}: {e}")
        else:
            adapted_values.append(None)  # Missing field -> NULL
            print(f"  ⚠️  Missing field {col}: NULL")
    
    return adapted_values, errors

def simulate_sql_generation(table_columns: List[str], schema_name: str, table_name: str, 
                          primary_keys: List[str]) -> str:
    """Simulate SQL query generation."""
    print("🔄 Step 5: Generating SQL query...")
    
    columns_str = ", ".join(f'"{col}"' for col in table_columns)
    placeholders = ", ".join(["%s"] * len(table_columns))
    
    if primary_keys:
        pk_columns = ", ".join(f'"{pk}"' for pk in primary_keys)
        update_columns = [col for col in table_columns if col not in primary_keys]
        update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)
        
        # Check if op_ts exists for timestamp-based conflict resolution
        has_op_ts = 'op_ts' in table_columns
        
        if has_op_ts:
            query = f'''
            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT ({pk_columns}) DO UPDATE SET
            {update_set}
            WHERE "{schema_name}"."{table_name}".op_ts < EXCLUDED.op_ts
            '''
            print(f"  ✅ Generated SQL with timestamp-based conflict resolution")
        else:
            query = f'''
            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT ({pk_columns}) DO UPDATE SET
            {update_set}
            '''
            print(f"  ✅ Generated SQL with standard conflict resolution")
    else:
        query = f'''
        INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
        VALUES ({placeholders})
        '''
        print(f"  ✅ Generated SQL without conflict resolution (no primary keys)")
    
    return query.strip()

def run_comprehensive_test():
    """Run the comprehensive pipeline test."""
    print("🧪 MSK Consumer Full Pipeline Test")
    print("=" * 50)
    
    # Test configuration
    schema_name = "test_schema"
    table_name = "payment_transactions"
    
    # Simulated table schema (based on common fields in sample data)
    table_columns = [
        'batch_id', 'file_ref_no', 'txn_ref_no', 'pymnt_amnt', 'pymnt_value_date',
        'pymnt_curr', 'ex_rate', 'status', 'debit_acc_no', 'order_cust_name',
        'bene_name', 'bene_acc_no', 'process_datetime', 'maker_date', 'checker_date',
        'transaction_type', 'debit_ccy', 'pymnt_debit_amnt', 'cid', 'op_ts'
    ]
    
    # Simulated primary keys
    primary_keys = ['batch_id', 'txn_ref_no']
    
    # Timestamp columns
    timestamp_columns = ['pymnt_value_date', 'process_datetime', 'maker_date', 'checker_date', 'op_ts']
    
    # Load sample data
    print("📄 Loading sample data...")
    sample_data = load_sample_data()
    if not sample_data:
        return False
    
    print(f"  ✅ Loaded {len(sample_data)} fields from sample data")
    
    try:
        # Step 1: Process Avro union types
        processed_data = simulate_avro_processing(sample_data)
        
        # Step 2: Convert timestamps
        processed_data = simulate_timestamp_conversion(processed_data, timestamp_columns)
        
        # Step 3: Validate fields
        validation_result = simulate_field_validation(processed_data, table_columns)
        
        # Step 4: Adapt values for PostgreSQL
        adapted_values, adaptation_errors = simulate_postgres_adaptation(processed_data, table_columns)
        
        # Step 5: Generate SQL
        sql_query = simulate_sql_generation(table_columns, schema_name, table_name, primary_keys)
        
        # Summary
        print("\n📊 Test Results Summary:")
        print("=" * 30)
        print(f"✅ Avro processing: SUCCESS")
        print(f"✅ Timestamp conversion: SUCCESS")
        print(f"✅ Field validation: {len(validation_result['matching_fields'])} matching, {len(validation_result['extra_fields'])} extra, {len(validation_result['missing_fields'])} missing")
        print(f"✅ PostgreSQL adaptation: {len(adapted_values)} values prepared")
        if adaptation_errors:
            print(f"⚠️  Adaptation errors: {len(adaptation_errors)}")
            for error in adaptation_errors[:3]:
                print(f"     - {error}")
        print(f"✅ SQL generation: SUCCESS")
        
        print(f"\n🔍 Sample SQL Query:")
        print("-" * 20)
        print(sql_query[:200] + "..." if len(sql_query) > 200 else sql_query)
        
        print(f"\n🎯 Pipeline Test: {'SUCCESS' if not adaptation_errors else 'SUCCESS WITH WARNINGS'}")
        return True
        
    except Exception as e:
        print(f"\n❌ Pipeline Test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """Test various error scenarios to ensure robust error handling."""
    print("\n🧪 Testing Error Scenarios")
    print("=" * 30)

    error_test_cases = [
        {
            'name': 'Out-of-range timestamp',
            'data': {'timestamp_field': 999999999999999999},  # Way too large
            'columns': ['timestamp_field'],
            'timestamp_cols': ['timestamp_field']
        },
        {
            'name': 'Invalid decimal format',
            'data': {'decimal_field': {'bytes': 'invalid_number'}},
            'columns': ['decimal_field'],
            'timestamp_cols': []
        },
        {
            'name': 'Complex nested data',
            'data': {'complex_field': {'nested': {'deep': {'value': 123}}}},
            'columns': ['complex_field'],
            'timestamp_cols': []
        },
        {
            'name': 'Very large string',
            'data': {'large_string': 'x' * 10000},
            'columns': ['large_string'],
            'timestamp_cols': []
        },
        {
            'name': 'Null and empty values',
            'data': {'null_field': None, 'empty_string': '', 'zero_value': 0},
            'columns': ['null_field', 'empty_string', 'zero_value'],
            'timestamp_cols': []
        }
    ]

    for i, test_case in enumerate(error_test_cases, 1):
        print(f"\n🔬 Error Test {i}: {test_case['name']}")
        try:
            # Process the test data
            processed = simulate_avro_processing(test_case['data'])
            processed = simulate_timestamp_conversion(processed, test_case['timestamp_cols'])
            validation = simulate_field_validation(processed, test_case['columns'])
            adapted_values, errors = simulate_postgres_adaptation(processed, test_case['columns'])

            if errors:
                print(f"  ⚠️  Expected errors found: {len(errors)}")
                for error in errors[:2]:
                    print(f"     - {error}")
            else:
                print(f"  ✅ Handled gracefully without errors")

        except Exception as e:
            print(f"  ❌ Unexpected exception: {e}")

def test_real_world_scenarios():
    """Test scenarios based on common real-world data issues."""
    print("\n🌍 Testing Real-World Scenarios")
    print("=" * 35)

    scenarios = [
        {
            'name': 'Mixed timestamp formats',
            'data': {
                'ts_seconds': 1640995200,           # Valid seconds
                'ts_millis': 1640995200000,         # Valid milliseconds
                'ts_micros': 1640995200000000,      # Valid microseconds
                'ts_invalid': 57414,                # The problematic value from your error
                'ts_too_large': 999999999999999999  # Too large
            }
        },
        {
            'name': 'Debezium CDC format',
            'data': {
                '__op': {'string': 'u'},
                '__ts_ms': {'long': 1750840530209},
                '__deleted': {'string': 'false'},
                'before': None,
                'after': {'id': 123, 'name': 'test'}
            }
        },
        {
            'name': 'Financial data precision',
            'data': {
                'amount_usd': {'bytes': 1000.123456789},
                'exchange_rate': {'bytes': 3.6920000000},
                'fee': {'bytes': '0E-9'},  # Scientific notation
                'balance': {'bytes': 999999999.99}
            }
        }
    ]

    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🔬 Scenario {i}: {scenario['name']}")
        try:
            processed = simulate_avro_processing(scenario['data'])
            processed = simulate_timestamp_conversion(processed, list(scenario['data'].keys()))
            print(f"  ✅ Processed {len(processed)} fields successfully")

            # Show sample results
            for key, value in list(processed.items())[:3]:
                print(f"     {key}: {value} ({type(value).__name__})")

        except Exception as e:
            print(f"  ❌ Failed: {e}")

def generate_test_report():
    """Generate a comprehensive test report."""
    print("\n📋 Test Report Generation")
    print("=" * 25)

    # Load sample data for analysis
    sample_data = load_sample_data()
    if not sample_data:
        return

    # Analyze data types
    type_analysis = {}
    for field, value in sample_data.items():
        if isinstance(value, dict):
            if 'bytes' in value:
                type_analysis[field] = f"decimal ({type(value['bytes']).__name__})"
            elif 'string' in value:
                type_analysis[field] = "string"
            elif 'long' in value:
                type_analysis[field] = "long"
            elif 'int' in value:
                type_analysis[field] = "int"
            else:
                type_analysis[field] = "unknown_union"
        else:
            type_analysis[field] = type(value).__name__

    # Group by type
    type_groups = {}
    for field, field_type in type_analysis.items():
        if field_type not in type_groups:
            type_groups[field_type] = []
        type_groups[field_type].append(field)

    print("📊 Data Type Analysis:")
    for data_type, fields in type_groups.items():
        print(f"  {data_type}: {len(fields)} fields")
        if len(fields) <= 5:
            print(f"    {', '.join(fields)}")
        else:
            print(f"    {', '.join(fields[:3])}, ... (+{len(fields)-3} more)")

    # Identify potential timestamp fields
    potential_timestamps = []
    for field, value in sample_data.items():
        if isinstance(value, dict) and 'long' in value:
            long_val = value['long']
            if isinstance(long_val, int) and long_val > 1000000000:  # Potential timestamp
                potential_timestamps.append((field, long_val))

    print(f"\n🕐 Potential Timestamp Fields: {len(potential_timestamps)}")
    for field, value in potential_timestamps[:5]:
        if value > 1000000000000000:
            ts_type = "microseconds"
            converted = datetime.fromtimestamp(value / 1000000).strftime('%Y-%m-%d %H:%M:%S')
        elif value > 1000000000000:
            ts_type = "milliseconds"
            converted = datetime.fromtimestamp(value / 1000).strftime('%Y-%m-%d %H:%M:%S')
        else:
            ts_type = "seconds"
            converted = datetime.fromtimestamp(value).strftime('%Y-%m-%d %H:%M:%S')

        print(f"  {field}: {value} ({ts_type}) -> {converted}")

if __name__ == "__main__":
    # Run all tests
    success = run_comprehensive_test()
    test_error_scenarios()
    test_real_world_scenarios()
    generate_test_report()

    print(f"\n🎯 Overall Test Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
