#!/usr/bin/env python3
"""
Comprehensive test script to simulate the full MSK consumer pipeline:
1. Create mock Avro schema and binary data
2. Test actual Avro decoding with fastavro
3. Process decimal and timestamp fields
4. Validate fields against target table schema
5. Simulate PostgreSQL operations with actual SQL generation
6. Test database connection and insertion simulation
7. Test error handling scenarios

This script helps identify potential issues before deploying to production.
"""

import json
import sys
import os
import io
import sqlite3
import tempfile
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Any, Optional
from unittest.mock import MagicMock, patch

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Try to import the actual modules
try:
    import fastavro
    FASTAVRO_AVAILABLE = True
except ImportError:
    print("⚠️  fastavro not available, using mock Avro processing")
    FASTAVRO_AVAILABLE = False

try:
    import psycopg2
    PSYCOPG2_AVAILABLE = True
except ImportError:
    print("⚠️  psycopg2 not available, using SQLite for database simulation")
    PSYCOPG2_AVAILABLE = False

def create_test_avro_schema() -> Dict:
    """Create a test Avro schema based on the sample data structure."""
    return {
        "type": "record",
        "name": "PaymentTransaction",
        "fields": [
            {"name": "BATCH_ID", "type": "int"},
            {"name": "FILE_REF_NO", "type": {"type": "bytes", "logicalType": "decimal", "precision": 20, "scale": 0}},
            {"name": "TXN_REF_NO", "type": "long"},
            {"name": "PYMNT_AMNT", "type": {"type": "bytes", "logicalType": "decimal", "precision": 15, "scale": 9}},
            {"name": "PYMNT_VALUE_DATE", "type": {"type": "long", "logicalType": "timestamp-millis"}},
            {"name": "PYMNT_CURR", "type": "string"},
            {"name": "EX_RATE", "type": {"type": "bytes", "logicalType": "decimal", "precision": 15, "scale": 10}},
            {"name": "STATUS", "type": "string"},
            {"name": "DEBIT_ACC_NO", "type": "string"},
            {"name": "PROCESS_DATETIME", "type": {"type": "long", "logicalType": "timestamp-millis"}},
            {"name": "STATUS_DATETIME", "type": {"type": "long", "logicalType": "timestamp-micros"}},
            {"name": "RECEIVE_DATETIME", "type": {"type": "long", "logicalType": "timestamp-micros"}},
            {"name": "ORG_BRANCH_CODE_OLD", "type": "int"},
            {"name": "__op", "type": "string"},
            {"name": "__ts_ms", "type": {"type": "long", "logicalType": "timestamp-millis"}}
        ]
    }

def create_test_avro_data() -> bytes:
    """Create test Avro binary data."""
    if not FASTAVRO_AVAILABLE:
        # Return mock binary data
        return b'\x00\x01\x02\x03\x04\x05'

    schema = create_test_avro_schema()

    # Create test record data
    test_record = {
        "BATCH_ID": 300008711,
        "FILE_REF_NO": (300008711420386015).to_bytes(8, byteorder='big', signed=True),
        "TXN_REF_NO": 420386015,
        "PYMNT_AMNT": (1000000000000).to_bytes(8, byteorder='big', signed=True),  # 1000.000000000
        "PYMNT_VALUE_DATE": 1750982400000,
        "PYMNT_CURR": "USD",
        "EX_RATE": (36920000000).to_bytes(8, byteorder='big', signed=True),  # 3.6920000000
        "STATUS": "10",
        "DEBIT_ACC_NO": "10619009393001",
        "PROCESS_DATETIME": 1750854821000,
        "STATUS_DATETIME": 1750854928435428,
        "RECEIVE_DATETIME": 1750854819471539,
        "ORG_BRANCH_CODE_OLD": 0,
        "__op": "u",
        "__ts_ms": 1750840530209
    }

    # Serialize to Avro binary format
    bytes_writer = io.BytesIO()
    fastavro.schemaless_writer(bytes_writer, schema, test_record)
    return bytes_writer.getvalue()

def load_sample_data(filename: str = 'pyhub_sample.txt') -> Dict:
    """Load sample data from file."""
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Sample file {filename} not found")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing JSON: {e}")
        return {}

def test_actual_avro_processing() -> Dict:
    """Test actual Avro schema parsing and binary decoding."""
    print("🔄 Step 1: Testing actual Avro processing...")

    try:
        # Import actual modules
        from postgres_utils import parse_schema_with_decimal, process_message

        schema = create_test_avro_schema()
        avro_data = create_test_avro_data()

        print(f"  ✅ Created Avro schema with {len(schema['fields'])} fields")
        print(f"  ✅ Created Avro binary data: {len(avro_data)} bytes")

        # Test schema parsing
        parsed_schema, decimal_fields = parse_schema_with_decimal(schema)
        print(f"  ✅ Parsed schema, found {len(decimal_fields)} decimal fields")

        # Test message processing
        if FASTAVRO_AVAILABLE:
            processed_record = process_message(avro_data, schema)
            print(f"  ✅ Processed Avro message: {len(processed_record)} fields")

            # Show sample processed fields
            sample_fields = {k: str(v)[:50] for k, v in list(processed_record.items())[:5]}
            for field, value in sample_fields.items():
                print(f"     {field}: {value}")

            return processed_record
        else:
            print("  ⚠️  Skipping binary decoding (fastavro not available)")
            return {}

    except ImportError as e:
        print(f"  ⚠️  Cannot import actual modules: {e}")
        return {}
    except Exception as e:
        print(f"  ❌ Avro processing failed: {e}")
        import traceback
        traceback.print_exc()
        return {}

def simulate_avro_processing(record: Dict) -> Dict:
    """Simulate the Avro union type processing."""
    print("🔄 Step 1: Processing Avro union types...")
    processed = {}
    
    for field_name, value in record.items():
        if isinstance(value, dict):
            if 'bytes' in value:
                try:
                    processed[field_name] = Decimal(str(value['bytes']))
                    print(f"  ✅ Decimal: {field_name} = {processed[field_name]}")
                except:
                    processed[field_name] = value['bytes']
                    print(f"  ⚠️  Decimal conversion failed: {field_name} = {processed[field_name]}")
            elif 'string' in value:
                processed[field_name] = value['string']
                print(f"  ✅ String: {field_name} = '{processed[field_name]}'")
            elif 'long' in value:
                processed[field_name] = value['long']
                print(f"  ✅ Long: {field_name} = {processed[field_name]}")
            elif 'int' in value:
                processed[field_name] = value['int']
                print(f"  ✅ Int: {field_name} = {processed[field_name]}")
            else:
                processed[field_name] = value
                print(f"  ⚠️  Unknown union: {field_name} = {value}")
        else:
            processed[field_name] = value
    
    return processed

def simulate_timestamp_conversion(record: Dict, timestamp_columns: List[str]) -> Dict:
    """Simulate timestamp conversion with bounds checking."""
    print("🔄 Step 2: Converting timestamps...")
    
    for field_name, value in record.items():
        if field_name.lower() in [col.lower() for col in timestamp_columns]:
            if isinstance(value, int):
                try:
                    if value > 1000000000000000:  # Microseconds
                        if value <= 253402300799999999:
                            converted = datetime.fromtimestamp(value / 1000000).strftime('%Y-%m-%d %H:%M:%S')
                            record[field_name] = converted
                            print(f"  ✅ Microsecond timestamp: {field_name} = {converted}")
                        else:
                            print(f"  ⚠️  Timestamp too large (microseconds): {field_name} = {value}")
                    elif value > 1000000000000:  # Milliseconds
                        if value <= 253402300799999:
                            converted = datetime.fromtimestamp(value / 1000).strftime('%Y-%m-%d %H:%M:%S')
                            record[field_name] = converted
                            print(f"  ✅ Millisecond timestamp: {field_name} = {converted}")
                        else:
                            print(f"  ⚠️  Timestamp too large (milliseconds): {field_name} = {value}")
                    elif value >= 946684800:  # Seconds
                        if value <= 253402300799:
                            converted = datetime.fromtimestamp(value).strftime('%Y-%m-%d %H:%M:%S')
                            record[field_name] = converted
                            print(f"  ✅ Second timestamp: {field_name} = {converted}")
                        else:
                            print(f"  ⚠️  Timestamp too large (seconds): {field_name} = {value}")
                    else:
                        print(f"  ⚠️  Timestamp too small: {field_name} = {value}")
                except Exception as e:
                    print(f"  ❌ Timestamp conversion error: {field_name} = {value}, error: {e}")
    
    return record

def simulate_field_validation(record: Dict, table_columns: List[str]) -> Dict:
    """Simulate field validation against target table schema."""
    print("🔄 Step 3: Validating fields against table schema...")
    
    # Exclude metadata fields
    record_fields = set(record.keys()) - {'_kafka_offset', '_raw_message'}
    table_columns_set = set(table_columns)
    
    extra_fields = record_fields - table_columns_set
    missing_fields = table_columns_set - record_fields
    matching_fields = record_fields & table_columns_set
    
    print(f"  📊 Field Analysis:")
    print(f"     Matching fields: {len(matching_fields)}")
    print(f"     Extra fields: {len(extra_fields)}")
    print(f"     Missing fields: {len(missing_fields)}")
    
    if extra_fields:
        print(f"  ⚠️  Extra fields (will be ignored): {sorted(list(extra_fields))[:5]}{'...' if len(extra_fields) > 5 else ''}")
    
    if missing_fields:
        print(f"  ⚠️  Missing fields (will be NULL): {sorted(list(missing_fields))[:5]}{'...' if len(missing_fields) > 5 else ''}")
    
    return {
        'valid': True,
        'extra_fields': list(extra_fields),
        'missing_fields': list(missing_fields),
        'matching_fields': list(matching_fields)
    }

def simulate_postgres_adaptation(record: Dict, table_columns: List[str]) -> List[Any]:
    """Simulate PostgreSQL value adaptation."""
    print("🔄 Step 4: Adapting values for PostgreSQL...")
    
    adapted_values = []
    errors = []
    
    for col in table_columns:
        if col in record:
            value = record[col]
            try:
                # Simulate adapt_value_for_postgres logic
                if value is None:
                    adapted_values.append(None)
                elif isinstance(value, Decimal):
                    adapted_values.append(float(value))  # PostgreSQL adapter converts Decimal to float
                elif isinstance(value, dict):
                    adapted_values.append(json.dumps(value))  # JSON serialization
                elif isinstance(value, (list, tuple)):
                    adapted_values.append(json.dumps(value))  # JSON serialization
                elif isinstance(value, bool):
                    adapted_values.append(value)
                elif isinstance(value, (int, float, str)):
                    adapted_values.append(value)
                else:
                    adapted_values.append(str(value))  # Convert to string as fallback
                
                print(f"  ✅ Adapted {col}: {type(value).__name__} -> {type(adapted_values[-1]).__name__}")
            except Exception as e:
                errors.append(f"Failed to adapt {col}: {e}")
                adapted_values.append(None)
                print(f"  ❌ Adaptation error {col}: {e}")
        else:
            adapted_values.append(None)  # Missing field -> NULL
            print(f"  ⚠️  Missing field {col}: NULL")
    
    return adapted_values, errors

def simulate_sql_generation(table_columns: List[str], schema_name: str, table_name: str, 
                          primary_keys: List[str]) -> str:
    """Simulate SQL query generation."""
    print("🔄 Step 5: Generating SQL query...")
    
    columns_str = ", ".join(f'"{col}"' for col in table_columns)
    placeholders = ", ".join(["%s"] * len(table_columns))
    
    if primary_keys:
        pk_columns = ", ".join(f'"{pk}"' for pk in primary_keys)
        update_columns = [col for col in table_columns if col not in primary_keys]
        update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)
        
        # Check if op_ts exists for timestamp-based conflict resolution
        has_op_ts = 'op_ts' in table_columns
        
        if has_op_ts:
            query = f'''
            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT ({pk_columns}) DO UPDATE SET
            {update_set}
            WHERE "{schema_name}"."{table_name}".op_ts < EXCLUDED.op_ts
            '''
            print(f"  ✅ Generated SQL with timestamp-based conflict resolution")
        else:
            query = f'''
            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT ({pk_columns}) DO UPDATE SET
            {update_set}
            '''
            print(f"  ✅ Generated SQL with standard conflict resolution")
    else:
        query = f'''
        INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
        VALUES ({placeholders})
        '''
        print(f"  ✅ Generated SQL without conflict resolution (no primary keys)")
    
    return query.strip()

def create_test_database():
    """Create a test database (SQLite) to simulate PostgreSQL operations with better type mapping."""
    print("🔄 Step 6: Creating test database...")

    # Create in-memory SQLite database
    conn = sqlite3.connect(':memory:')
    cursor = conn.cursor()

    # Enable foreign key constraints (more PostgreSQL-like)
    cursor.execute("PRAGMA foreign_keys = ON")

    # Create test table with PostgreSQL-like schema
    create_table_sql = '''
    CREATE TABLE test_schema_payment_transactions (
        batch_id INTEGER NOT NULL,
        file_ref_no NUMERIC(20,0),
        txn_ref_no BIGINT NOT NULL,
        pymnt_amnt NUMERIC(15,9),
        pymnt_value_date TIMESTAMP,
        pymnt_curr VARCHAR(3),
        ex_rate NUMERIC(15,10),
        status VARCHAR(10),
        debit_acc_no VARCHAR(50),
        order_cust_name VARCHAR(255),
        bene_name VARCHAR(255),
        bene_acc_no VARCHAR(50),
        process_datetime TIMESTAMP,
        maker_date TIMESTAMP,
        checker_date TIMESTAMP,
        transaction_type VARCHAR(10),
        debit_ccy VARCHAR(3),
        pymnt_debit_amnt NUMERIC(15,9),
        cid VARCHAR(20),
        op_ts TIMESTAMP,
        PRIMARY KEY (batch_id, txn_ref_no),
        CHECK (pymnt_amnt >= 0),
        CHECK (LENGTH(pymnt_curr) = 3),
        CHECK (LENGTH(debit_ccy) = 3)
    )
    '''

    cursor.execute(create_table_sql)
    print(f"  ✅ Created test table with PostgreSQL-like schema")
    print(f"     - NUMERIC types for precise decimals")
    print(f"     - VARCHAR with length constraints")
    print(f"     - CHECK constraints for data validation")
    print(f"     - Composite primary key")

    return conn, cursor

def test_database_operations(adapted_values: List[Any], table_columns: List[str]):
    """Test actual database operations."""
    print("🔄 Step 7: Testing database operations...")

    try:
        conn, cursor = create_test_database()

        # Test INSERT operation
        placeholders = ', '.join(['?' for _ in table_columns])
        insert_sql = f"INSERT INTO test_schema_payment_transactions ({', '.join(table_columns)}) VALUES ({placeholders})"

        print(f"  📝 SQL: {insert_sql[:100]}...")

        # Execute the insert
        cursor.execute(insert_sql, adapted_values)
        conn.commit()

        print(f"  ✅ INSERT successful: 1 row inserted")

        # Test SELECT to verify data
        cursor.execute("SELECT COUNT(*) FROM test_schema_payment_transactions")
        count = cursor.fetchone()[0]
        print(f"  ✅ SELECT verification: {count} rows in table")

        # Test PostgreSQL-style UPSERT (simulate ON CONFLICT)
        # SQLite doesn't support ON CONFLICT DO UPDATE, so we simulate it
        print(f"  🔄 Testing PostgreSQL-style conflict resolution...")

        # First, try to insert the same primary key values again (should conflict)
        try:
            cursor.execute(insert_sql, adapted_values)
            print(f"  ❌ Expected conflict but insert succeeded")
        except sqlite3.IntegrityError:
            print(f"  ✅ Primary key conflict detected (as expected)")

        # Simulate ON CONFLICT DO UPDATE by doing UPDATE WHERE EXISTS
        modified_values = adapted_values.copy()
        if len(modified_values) > 3:
            modified_values[3] = 2000.0  # Change amount

        # Simulate PostgreSQL ON CONFLICT behavior
        update_sql = f'''
        UPDATE test_schema_payment_transactions
        SET pymnt_amnt = ?
        WHERE batch_id = ? AND txn_ref_no = ?
        '''
        cursor.execute(update_sql, [modified_values[3], adapted_values[0], adapted_values[2]])
        conn.commit()

        print(f"  ✅ Conflict resolution (UPDATE) successful")

        # Test data types and constraints
        cursor.execute("SELECT * FROM test_schema_payment_transactions LIMIT 1")
        row = cursor.fetchone()
        if row:
            print(f"  ✅ Data verification: Retrieved {len(row)} columns")
            # Show sample data
            for i, (col, val) in enumerate(zip(table_columns[:5], row[:5])):
                print(f"     {col}: {val} ({type(val).__name__})")

        conn.close()
        return True

    except Exception as e:
        print(f"  ❌ Database operation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_postgres_utils_integration():
    """Test integration with actual postgres_utils functions."""
    print("🔄 Step 8: Testing postgres_utils integration...")

    try:
        # Test imports
        from postgres_utils import (
            get_table_columns, get_column_types, validate_record_fields,
            convert_decimal_fields, convert_date_fields
        )
        from postgres_batch_utils import adapt_value_for_postgres

        print("  ✅ Successfully imported postgres_utils functions")

        # Test field validation
        test_record = {
            'batch_id': 123,
            'pymnt_amnt': Decimal('1000.50'),
            'pymnt_curr': 'USD',
            'extra_field': 'should_be_ignored'
        }

        test_columns = ['batch_id', 'pymnt_amnt', 'pymnt_curr', 'missing_field']

        validation_result = validate_record_fields(
            test_record, test_columns, 'test_schema', 'test_table'
        )

        print(f"  ✅ Field validation: {len(validation_result['matching_fields'])} matching, "
              f"{len(validation_result['extra_fields'])} extra, {len(validation_result['missing_fields'])} missing")

        # Test value adaptation
        adapted_values = []
        for col in test_columns:
            if col in test_record:
                adapted = adapt_value_for_postgres(test_record[col])
                adapted_values.append(adapted)
                print(f"     {col}: {test_record[col]} -> {adapted} ({type(adapted).__name__})")
            else:
                adapted_values.append(None)
                print(f"     {col}: NULL")

        return True

    except ImportError as e:
        print(f"  ⚠️  Cannot import postgres_utils: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Integration test failed: {e}")
        return False

def test_postgresql_specific_features():
    """Test PostgreSQL-specific features that SQLite can't fully simulate."""
    print("🔄 Step 9: Testing PostgreSQL-specific features...")

    postgresql_features = {
        'NUMERIC precision': {
            'test': 'Decimal(\'1000.123456789\')',
            'expected': 'Should preserve exact precision',
            'sqlite_limitation': 'SQLite converts to REAL (loses precision)'
        },
        'TIMESTAMP handling': {
            'test': '\'2025-06-25 16:33:41\'',
            'expected': 'Native timestamp operations',
            'sqlite_limitation': 'SQLite stores as TEXT'
        },
        'VARCHAR constraints': {
            'test': 'String longer than VARCHAR(3)',
            'expected': 'Should raise constraint violation',
            'sqlite_limitation': 'SQLite allows any length'
        },
        'Schema namespaces': {
            'test': 'schema.table references',
            'expected': 'Full schema.table support',
            'sqlite_limitation': 'No schema support'
        },
        'ON CONFLICT syntax': {
            'test': 'ON CONFLICT (pk) DO UPDATE SET...',
            'expected': 'Native upsert support',
            'sqlite_limitation': 'Uses INSERT OR REPLACE'
        }
    }

    print("  📊 PostgreSQL vs SQLite Feature Comparison:")
    for feature, details in postgresql_features.items():
        print(f"     {feature}:")
        print(f"       Test: {details['test']}")
        print(f"       PostgreSQL: {details['expected']}")
        print(f"       SQLite: {details['sqlite_limitation']}")

    # Test actual PostgreSQL connection if available
    if PSYCOPG2_AVAILABLE:
        print("  🔄 Testing actual PostgreSQL connection...")
        try:
            # This would require actual PostgreSQL credentials
            # For now, just test the import and basic functionality
            import psycopg2
            print("  ✅ psycopg2 available for real PostgreSQL testing")
            print("  ⚠️  Skipping actual connection (requires credentials)")
        except Exception as e:
            print(f"  ❌ PostgreSQL connection test failed: {e}")
    else:
        print("  ⚠️  psycopg2 not available, cannot test real PostgreSQL")

    return True

def create_postgresql_test_recommendations():
    """Generate recommendations for more accurate PostgreSQL testing."""
    print("🔄 Step 10: PostgreSQL Testing Recommendations...")

    recommendations = [
        {
            'category': 'Data Type Testing',
            'recommendation': 'Use actual PostgreSQL for NUMERIC precision testing',
            'reason': 'SQLite REAL type loses decimal precision',
            'implementation': 'Create test with psycopg2 and real PostgreSQL instance'
        },
        {
            'category': 'Constraint Testing',
            'recommendation': 'Test VARCHAR length constraints in PostgreSQL',
            'reason': 'SQLite ignores VARCHAR length limits',
            'implementation': 'Insert strings longer than VARCHAR(n) and expect errors'
        },
        {
            'category': 'Timestamp Testing',
            'recommendation': 'Test native TIMESTAMP operations',
            'reason': 'SQLite stores timestamps as TEXT',
            'implementation': 'Use PostgreSQL TIMESTAMP functions and comparisons'
        },
        {
            'category': 'Schema Testing',
            'recommendation': 'Test schema.table references',
            'reason': 'SQLite has no schema namespace support',
            'implementation': 'Create multiple schemas and test cross-schema operations'
        },
        {
            'category': 'Conflict Resolution',
            'recommendation': 'Test actual ON CONFLICT DO UPDATE syntax',
            'reason': 'SQLite uses different syntax (INSERT OR REPLACE)',
            'implementation': 'Test with composite primary keys and partial updates'
        }
    ]

    print("  📋 Recommendations for Production-Ready Testing:")
    for i, rec in enumerate(recommendations, 1):
        print(f"     {i}. {rec['category']}")
        print(f"        Issue: {rec['reason']}")
        print(f"        Solution: {rec['recommendation']}")
        print(f"        Implementation: {rec['implementation']}")
        print()

    return recommendations

def run_comprehensive_test():
    """Run the comprehensive pipeline test."""
    print("🧪 MSK Consumer Full Pipeline Test")
    print("=" * 50)
    
    # Test configuration
    schema_name = "test_schema"
    table_name = "payment_transactions"
    
    # Simulated table schema (matching actual sample data field names)
    table_columns = [
        'BATCH_ID', 'FILE_REF_NO', 'TXN_REF_NO', 'PYMNT_AMNT', 'PYMNT_VALUE_DATE',
        'PYMNT_CURR', 'EX_RATE', 'STATUS', 'DEBIT_ACC_NO', 'ORDER_CUST_NAME',
        'BENE_NAME', 'BENE_ACC_NO', 'PROCESS_DATETIME', 'MAKER_DATE', 'CHECKER_DATE',
        'TRANSACTION_TYPE', 'DEBIT_CCY', 'PYMNT_DEBIT_AMNT', 'CID', 'op_ts'
    ]
    
    # Simulated primary keys
    primary_keys = ['BATCH_ID', 'TXN_REF_NO']

    # Timestamp columns
    timestamp_columns = ['PYMNT_VALUE_DATE', 'PROCESS_DATETIME', 'MAKER_DATE', 'CHECKER_DATE', 'op_ts']
    
    # Load sample data
    print("📄 Loading sample data...")
    sample_data = load_sample_data()
    if not sample_data:
        return False
    
    print(f"  ✅ Loaded {len(sample_data)} fields from sample data")
    
    try:
        # Step 1: Test actual Avro processing
        avro_processed_data = test_actual_avro_processing()

        # Step 2: Process sample data (fallback if Avro fails)
        if not avro_processed_data:
            print("  ⚠️  Using sample data fallback")
            processed_data = simulate_avro_processing(sample_data)
        else:
            processed_data = avro_processed_data

        # Step 3: Convert timestamps
        processed_data = simulate_timestamp_conversion(processed_data, timestamp_columns)

        # Step 4: Validate fields
        validation_result = simulate_field_validation(processed_data, table_columns)

        # Step 5: Adapt values for PostgreSQL
        adapted_values, adaptation_errors = simulate_postgres_adaptation(processed_data, table_columns)

        # Step 6: Generate SQL
        sql_query = simulate_sql_generation(table_columns, schema_name, table_name, primary_keys)

        # Step 7: Test database operations
        db_success = test_database_operations(adapted_values, table_columns)

        # Step 8: Test postgres_utils integration
        integration_success = test_postgres_utils_integration()

        # Step 9: Test PostgreSQL-specific features
        postgresql_test_success = test_postgresql_specific_features()

        # Step 10: Generate PostgreSQL testing recommendations
        recommendations = create_postgresql_test_recommendations()

        # Summary
        print("\n📊 Test Results Summary:")
        print("=" * 30)
        print(f"✅ Avro processing: {'SUCCESS' if avro_processed_data else 'FALLBACK'}")
        print(f"✅ Timestamp conversion: SUCCESS")
        print(f"✅ Field validation: {len(validation_result['matching_fields'])} matching, {len(validation_result['extra_fields'])} extra, {len(validation_result['missing_fields'])} missing")
        print(f"✅ PostgreSQL adaptation: {len(adapted_values)} values prepared")
        if adaptation_errors:
            print(f"⚠️  Adaptation errors: {len(adaptation_errors)}")
            for error in adaptation_errors[:3]:
                print(f"     - {error}")
        print(f"✅ SQL generation: SUCCESS")
        print(f"✅ Database operations (SQLite): {'SUCCESS' if db_success else 'FAILED'}")
        print(f"✅ Integration tests: {'SUCCESS' if integration_success else 'FAILED'}")
        print(f"✅ PostgreSQL feature analysis: {'SUCCESS' if postgresql_test_success else 'FAILED'}")

        print(f"\n🔍 Sample SQL Query:")
        print("-" * 20)
        print(sql_query[:200] + "..." if len(sql_query) > 200 else sql_query)

        print(f"\n⚠️  SQLite vs PostgreSQL Limitations:")
        print("   - NUMERIC precision: SQLite uses REAL (loses precision)")
        print("   - TIMESTAMP handling: SQLite stores as TEXT")
        print("   - VARCHAR constraints: SQLite ignores length limits")
        print("   - Schema namespaces: SQLite has no schema support")
        print("   - ON CONFLICT syntax: Different between SQLite and PostgreSQL")

        overall_success = db_success and integration_success and not adaptation_errors
        print(f"\n🎯 Pipeline Test: {'SUCCESS' if overall_success else 'SUCCESS WITH WARNINGS'}")
        print(f"🎯 PostgreSQL Accuracy: PARTIAL (SQLite simulation has limitations)")
        print(f"💡 Recommendation: Run additional tests with actual PostgreSQL for production validation")

        return overall_success
        
    except Exception as e:
        print(f"\n❌ Pipeline Test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """Test various error scenarios to ensure robust error handling."""
    print("\n🧪 Testing Error Scenarios")
    print("=" * 30)

    error_test_cases = [
        {
            'name': 'Out-of-range timestamp',
            'data': {'timestamp_field': 999999999999999999},  # Way too large
            'columns': ['timestamp_field'],
            'timestamp_cols': ['timestamp_field']
        },
        {
            'name': 'Invalid decimal format',
            'data': {'decimal_field': {'bytes': 'invalid_number'}},
            'columns': ['decimal_field'],
            'timestamp_cols': []
        },
        {
            'name': 'Complex nested data',
            'data': {'complex_field': {'nested': {'deep': {'value': 123}}}},
            'columns': ['complex_field'],
            'timestamp_cols': []
        },
        {
            'name': 'Very large string',
            'data': {'large_string': 'x' * 10000},
            'columns': ['large_string'],
            'timestamp_cols': []
        },
        {
            'name': 'Null and empty values',
            'data': {'null_field': None, 'empty_string': '', 'zero_value': 0},
            'columns': ['null_field', 'empty_string', 'zero_value'],
            'timestamp_cols': []
        }
    ]

    for i, test_case in enumerate(error_test_cases, 1):
        print(f"\n🔬 Error Test {i}: {test_case['name']}")
        try:
            # Process the test data
            processed = simulate_avro_processing(test_case['data'])
            processed = simulate_timestamp_conversion(processed, test_case['timestamp_cols'])
            validation = simulate_field_validation(processed, test_case['columns'])
            adapted_values, errors = simulate_postgres_adaptation(processed, test_case['columns'])

            if errors:
                print(f"  ⚠️  Expected errors found: {len(errors)}")
                for error in errors[:2]:
                    print(f"     - {error}")
            else:
                print(f"  ✅ Handled gracefully without errors")

        except Exception as e:
            print(f"  ❌ Unexpected exception: {e}")

def test_real_world_scenarios():
    """Test scenarios based on common real-world data issues."""
    print("\n🌍 Testing Real-World Scenarios")
    print("=" * 35)

    scenarios = [
        {
            'name': 'Mixed timestamp formats',
            'data': {
                'ts_seconds': 1640995200,           # Valid seconds
                'ts_millis': 1640995200000,         # Valid milliseconds
                'ts_micros': 1640995200000000,      # Valid microseconds
                'ts_invalid': 57414,                # The problematic value from your error
                'ts_too_large': 999999999999999999  # Too large
            }
        },
        {
            'name': 'Debezium CDC format',
            'data': {
                '__op': {'string': 'u'},
                '__ts_ms': {'long': 1750840530209},
                '__deleted': {'string': 'false'},
                'before': None,
                'after': {'id': 123, 'name': 'test'}
            }
        },
        {
            'name': 'Financial data precision',
            'data': {
                'amount_usd': {'bytes': 1000.123456789},
                'exchange_rate': {'bytes': 3.6920000000},
                'fee': {'bytes': '0E-9'},  # Scientific notation
                'balance': {'bytes': 999999999.99}
            }
        }
    ]

    for i, scenario in enumerate(scenarios, 1):
        print(f"\n🔬 Scenario {i}: {scenario['name']}")
        try:
            processed = simulate_avro_processing(scenario['data'])
            processed = simulate_timestamp_conversion(processed, list(scenario['data'].keys()))
            print(f"  ✅ Processed {len(processed)} fields successfully")

            # Show sample results
            for key, value in list(processed.items())[:3]:
                print(f"     {key}: {value} ({type(value).__name__})")

        except Exception as e:
            print(f"  ❌ Failed: {e}")

def generate_test_report():
    """Generate a comprehensive test report."""
    print("\n📋 Test Report Generation")
    print("=" * 25)

    # Load sample data for analysis
    sample_data = load_sample_data()
    if not sample_data:
        return

    # Analyze data types
    type_analysis = {}
    for field, value in sample_data.items():
        if isinstance(value, dict):
            if 'bytes' in value:
                type_analysis[field] = f"decimal ({type(value['bytes']).__name__})"
            elif 'string' in value:
                type_analysis[field] = "string"
            elif 'long' in value:
                type_analysis[field] = "long"
            elif 'int' in value:
                type_analysis[field] = "int"
            else:
                type_analysis[field] = "unknown_union"
        else:
            type_analysis[field] = type(value).__name__

    # Group by type
    type_groups = {}
    for field, field_type in type_analysis.items():
        if field_type not in type_groups:
            type_groups[field_type] = []
        type_groups[field_type].append(field)

    print("📊 Data Type Analysis:")
    for data_type, fields in type_groups.items():
        print(f"  {data_type}: {len(fields)} fields")
        if len(fields) <= 5:
            print(f"    {', '.join(fields)}")
        else:
            print(f"    {', '.join(fields[:3])}, ... (+{len(fields)-3} more)")

    # Identify potential timestamp fields
    potential_timestamps = []
    for field, value in sample_data.items():
        if isinstance(value, dict) and 'long' in value:
            long_val = value['long']
            if isinstance(long_val, int) and long_val > 1000000000:  # Potential timestamp
                potential_timestamps.append((field, long_val))

    print(f"\n🕐 Potential Timestamp Fields: {len(potential_timestamps)}")
    for field, value in potential_timestamps[:5]:
        if value > 1000000000000000:
            ts_type = "microseconds"
            converted = datetime.fromtimestamp(value / 1000000).strftime('%Y-%m-%d %H:%M:%S')
        elif value > 1000000000000:
            ts_type = "milliseconds"
            converted = datetime.fromtimestamp(value / 1000).strftime('%Y-%m-%d %H:%M:%S')
        else:
            ts_type = "seconds"
            converted = datetime.fromtimestamp(value).strftime('%Y-%m-%d %H:%M:%S')

        print(f"  {field}: {value} ({ts_type}) -> {converted}")

if __name__ == "__main__":
    # Run all tests
    success = run_comprehensive_test()
    test_error_scenarios()
    test_real_world_scenarios()
    generate_test_report()

    print(f"\n🎯 Overall Test Result: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
