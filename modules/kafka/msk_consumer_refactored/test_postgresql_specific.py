#!/usr/bin/env python3
"""
PostgreSQL-specific test script that requires actual PostgreSQL connection.
This script tests features that SQLite cannot accurately simulate:
1. NUMERIC precision and scale
2. Native TIMESTAMP operations
3. VARCHAR length constraints
4. Schema namespace support
5. ON CONFLICT DO UPDATE syntax
6. CHECK constraints
7. Foreign key constraints

Usage:
    export POSTGRES_URL="postgresql://user:pass@host:port/dbname"
    python3 test_postgresql_specific.py

Or set individual environment variables:
    export POSTGRES_HOST=localhost
    export POSTGRES_PORT=5432
    export POSTGRES_DB=test_db
    export POSTGRES_USER=test_user
    export POSTGRES_PASSWORD=test_pass
"""

import os
import sys
import json
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Any, Optional

# Try to import PostgreSQL dependencies
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    PSYCOPG2_AVAILABLE = True
except ImportError:
    print("❌ psycopg2 not available. Install with: pip install psycopg2-binary")
    sys.exit(1)

def get_postgres_connection():
    """Get PostgreSQL connection from environment variables."""
    # Try connection string first
    postgres_url = os.getenv('POSTGRES_URL')
    if postgres_url:
        return psycopg2.connect(postgres_url)
    
    # Try individual parameters
    conn_params = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'port': os.getenv('POSTGRES_PORT', '5432'),
        'database': os.getenv('POSTGRES_DB', 'test_db'),
        'user': os.getenv('POSTGRES_USER', 'test_user'),
        'password': os.getenv('POSTGRES_PASSWORD', 'test_pass')
    }
    
    return psycopg2.connect(**conn_params)

def create_test_schema_and_table(cursor):
    """Create test schema and table with PostgreSQL-specific features."""
    print("🔄 Creating test schema and table...")
    
    # Create schema
    cursor.execute("CREATE SCHEMA IF NOT EXISTS test_msk_consumer")
    
    # Drop table if exists
    cursor.execute("DROP TABLE IF EXISTS test_msk_consumer.payment_transactions")
    
    # Create table with PostgreSQL-specific types and constraints
    create_table_sql = '''
    CREATE TABLE test_msk_consumer.payment_transactions (
        batch_id INTEGER NOT NULL,
        file_ref_no NUMERIC(20,0),
        txn_ref_no BIGINT NOT NULL,
        pymnt_amnt NUMERIC(15,9) CHECK (pymnt_amnt >= 0),
        pymnt_value_date TIMESTAMP WITHOUT TIME ZONE,
        pymnt_curr VARCHAR(3) CHECK (LENGTH(pymnt_curr) = 3),
        ex_rate NUMERIC(15,10) CHECK (ex_rate > 0),
        status VARCHAR(10),
        debit_acc_no VARCHAR(50),
        order_cust_name VARCHAR(255),
        bene_name VARCHAR(255),
        bene_acc_no VARCHAR(50),
        process_datetime TIMESTAMP WITHOUT TIME ZONE,
        maker_date TIMESTAMP WITHOUT TIME ZONE,
        checker_date TIMESTAMP WITHOUT TIME ZONE,
        transaction_type VARCHAR(10),
        debit_ccy VARCHAR(3) CHECK (LENGTH(debit_ccy) = 3),
        pymnt_debit_amnt NUMERIC(15,9),
        cid VARCHAR(20),
        op_ts TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        PRIMARY KEY (batch_id, txn_ref_no),
        
        CONSTRAINT valid_currency_codes 
            CHECK (pymnt_curr ~ '^[A-Z]{3}$' AND debit_ccy ~ '^[A-Z]{3}$'),
        CONSTRAINT valid_status 
            CHECK (status IN ('10', '20', '30', '40', '50')),
        CONSTRAINT valid_transaction_type 
            CHECK (transaction_type IN ('CREDIT', 'DEBIT', 'TRANSFER'))
    )
    '''
    
    cursor.execute(create_table_sql)
    print("  ✅ Created schema and table with PostgreSQL-specific features")
    print("     - NUMERIC types with precision and scale")
    print("     - TIMESTAMP WITHOUT TIME ZONE")
    print("     - VARCHAR with length constraints")
    print("     - CHECK constraints with regex patterns")
    print("     - Composite primary key")
    print("     - DEFAULT values")

def test_numeric_precision(cursor):
    """Test NUMERIC precision and scale handling."""
    print("\n🔄 Testing NUMERIC precision and scale...")
    
    test_cases = [
        {
            'name': 'High precision decimal',
            'value': Decimal('1000.123456789'),
            'expected_precision': 9
        },
        {
            'name': 'Exchange rate precision',
            'value': Decimal('3.6920000000'),
            'expected_precision': 10
        },
        {
            'name': 'Large amount',
            'value': Decimal('999999999.999999999'),
            'expected_precision': 9
        }
    ]
    
    for test_case in test_cases:
        try:
            # Insert test value
            cursor.execute('''
                INSERT INTO test_msk_consumer.payment_transactions 
                (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status, transaction_type)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            ''', (1, 1, test_case['value'], 'USD', 'USD', '10', 'CREDIT'))
            
            # Retrieve and verify precision
            cursor.execute('''
                SELECT pymnt_amnt FROM test_msk_consumer.payment_transactions 
                WHERE batch_id = 1 AND txn_ref_no = 1
            ''')
            
            result = cursor.fetchone()[0]
            
            # Check if precision is preserved
            if str(result) == str(test_case['value']):
                print(f"  ✅ {test_case['name']}: Precision preserved ({result})")
            else:
                print(f"  ⚠️  {test_case['name']}: Precision changed ({test_case['value']} -> {result})")
            
            # Clean up for next test
            cursor.execute('DELETE FROM test_msk_consumer.payment_transactions WHERE batch_id = 1')
            
        except Exception as e:
            print(f"  ❌ {test_case['name']}: Failed - {e}")

def test_timestamp_operations(cursor):
    """Test native TIMESTAMP operations."""
    print("\n🔄 Testing TIMESTAMP operations...")
    
    # Insert record with timestamps
    now = datetime.now()
    cursor.execute('''
        INSERT INTO test_msk_consumer.payment_transactions 
        (batch_id, txn_ref_no, pymnt_value_date, process_datetime, op_ts, 
         pymnt_curr, debit_ccy, status, transaction_type)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    ''', (2, 1, now, now, now, 'USD', 'USD', '10', 'CREDIT'))
    
    # Test timestamp comparisons
    cursor.execute('''
        SELECT 
            pymnt_value_date,
            process_datetime,
            op_ts,
            EXTRACT(EPOCH FROM op_ts) as epoch_seconds,
            op_ts AT TIME ZONE 'UTC' as utc_time
        FROM test_msk_consumer.payment_transactions 
        WHERE batch_id = 2
    ''')
    
    result = cursor.fetchone()
    print(f"  ✅ TIMESTAMP storage: {result[0]}")
    print(f"  ✅ EPOCH extraction: {result[3]}")
    print(f"  ✅ Timezone conversion: {result[4]}")
    
    # Test timestamp arithmetic
    cursor.execute('''
        SELECT 
            op_ts + INTERVAL '1 day' as tomorrow,
            AGE(CURRENT_TIMESTAMP, op_ts) as age
        FROM test_msk_consumer.payment_transactions 
        WHERE batch_id = 2
    ''')
    
    result = cursor.fetchone()
    print(f"  ✅ Date arithmetic: +1 day = {result[0]}")
    print(f"  ✅ Age calculation: {result[1]}")

def test_constraint_violations(cursor):
    """Test constraint violations."""
    print("\n🔄 Testing constraint violations...")
    
    constraint_tests = [
        {
            'name': 'VARCHAR length violation',
            'sql': '''INSERT INTO test_msk_consumer.payment_transactions 
                     (batch_id, txn_ref_no, pymnt_curr, debit_ccy, status, transaction_type)
                     VALUES (3, 1, 'TOOLONG', 'USD', '10', 'CREDIT')''',
            'expected_error': 'value too long'
        },
        {
            'name': 'CHECK constraint violation (negative amount)',
            'sql': '''INSERT INTO test_msk_consumer.payment_transactions 
                     (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status, transaction_type)
                     VALUES (3, 2, -100.00, 'USD', 'USD', '10', 'CREDIT')''',
            'expected_error': 'check constraint'
        },
        {
            'name': 'Invalid status value',
            'sql': '''INSERT INTO test_msk_consumer.payment_transactions 
                     (batch_id, txn_ref_no, pymnt_curr, debit_ccy, status, transaction_type)
                     VALUES (3, 3, 'USD', 'USD', 'INVALID', 'CREDIT')''',
            'expected_error': 'check constraint'
        }
    ]
    
    for test in constraint_tests:
        try:
            cursor.execute(test['sql'])
            print(f"  ❌ {test['name']}: Should have failed but succeeded")
        except psycopg2.Error as e:
            if test['expected_error'].lower() in str(e).lower():
                print(f"  ✅ {test['name']}: Correctly rejected ({e.pgcode})")
            else:
                print(f"  ⚠️  {test['name']}: Unexpected error - {e}")

def test_on_conflict_syntax(cursor):
    """Test PostgreSQL ON CONFLICT DO UPDATE syntax."""
    print("\n🔄 Testing ON CONFLICT DO UPDATE...")
    
    # Insert initial record
    cursor.execute('''
        INSERT INTO test_msk_consumer.payment_transactions 
        (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status, transaction_type)
        VALUES (4, 1, 1000.00, 'USD', 'USD', '10', 'CREDIT')
    ''')
    
    # Test ON CONFLICT DO UPDATE
    cursor.execute('''
        INSERT INTO test_msk_consumer.payment_transactions 
        (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status, transaction_type, op_ts)
        VALUES (4, 1, 2000.00, 'EUR', 'EUR', '20', 'DEBIT', CURRENT_TIMESTAMP)
        ON CONFLICT (batch_id, txn_ref_no) 
        DO UPDATE SET 
            pymnt_amnt = EXCLUDED.pymnt_amnt,
            pymnt_curr = EXCLUDED.pymnt_curr,
            debit_ccy = EXCLUDED.debit_ccy,
            status = EXCLUDED.status,
            transaction_type = EXCLUDED.transaction_type,
            op_ts = EXCLUDED.op_ts
        WHERE test_msk_consumer.payment_transactions.op_ts < EXCLUDED.op_ts
    ''')
    
    # Verify the update
    cursor.execute('''
        SELECT pymnt_amnt, pymnt_curr, status, transaction_type 
        FROM test_msk_consumer.payment_transactions 
        WHERE batch_id = 4 AND txn_ref_no = 1
    ''')
    
    result = cursor.fetchone()
    print(f"  ✅ ON CONFLICT DO UPDATE successful")
    print(f"     Updated amount: {result[0]}")
    print(f"     Updated currency: {result[1]}")
    print(f"     Updated status: {result[2]}")
    print(f"     Updated type: {result[3]}")

def run_postgresql_tests():
    """Run all PostgreSQL-specific tests."""
    print("🧪 PostgreSQL-Specific Feature Tests")
    print("=" * 40)
    
    try:
        # Connect to PostgreSQL
        print("🔄 Connecting to PostgreSQL...")
        conn = get_postgres_connection()
        cursor = conn.cursor()
        
        print(f"  ✅ Connected to PostgreSQL")
        
        # Get PostgreSQL version
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        print(f"  📊 PostgreSQL version: {version.split(',')[0]}")
        
        # Run tests
        create_test_schema_and_table(cursor)
        conn.commit()
        
        test_numeric_precision(cursor)
        conn.commit()
        
        test_timestamp_operations(cursor)
        conn.commit()
        
        test_constraint_violations(cursor)
        conn.rollback()  # Rollback constraint violations
        
        test_on_conflict_syntax(cursor)
        conn.commit()
        
        # Cleanup
        cursor.execute("DROP SCHEMA test_msk_consumer CASCADE")
        conn.commit()
        
        print("\n✅ All PostgreSQL tests completed successfully!")
        print("🎯 Result: PostgreSQL features work as expected")
        
        cursor.close()
        conn.close()
        
        return True
        
    except psycopg2.Error as e:
        print(f"\n❌ PostgreSQL error: {e}")
        print(f"   Error code: {e.pgcode}")
        print(f"   Error details: {e.pgerror}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    if not PSYCOPG2_AVAILABLE:
        print("❌ psycopg2 not available")
        sys.exit(1)
    
    # Check for connection parameters
    if not (os.getenv('POSTGRES_URL') or all([
        os.getenv('POSTGRES_HOST'),
        os.getenv('POSTGRES_DB'),
        os.getenv('POSTGRES_USER'),
        os.getenv('POSTGRES_PASSWORD')
    ])):
        print("❌ PostgreSQL connection parameters not set")
        print("Set POSTGRES_URL or individual POSTGRES_* environment variables")
        sys.exit(1)
    
    success = run_postgresql_tests()
    sys.exit(0 if success else 1)
