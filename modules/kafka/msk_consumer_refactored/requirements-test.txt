# Testing dependencies for MSK Consumer
# Install with: pip install -r requirements-test.txt

# Core testing dependencies
# For standalone tests (no dependencies required)

# For full pipeline tests
fastavro>=1.4.0

# For real PostgreSQL testing (100% accuracy)
testing.postgresql>=1.3.0
psycopg2-binary>=2.9.0

# Note: PostgreSQL must be installed on system for testing.postgresql
# macOS: brew install postgresql
# Ubuntu: sudo apt-get install postgresql postgresql-contrib
# CentOS/RHEL: sudo yum install postgresql postgresql-server
