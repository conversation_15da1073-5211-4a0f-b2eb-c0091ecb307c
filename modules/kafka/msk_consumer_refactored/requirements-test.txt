# Testing dependencies for MSK Consumer
# Install with: pip install -r requirements-test.txt

# PostgreSQL testing with real PostgreSQL instances
testing.postgresql>=1.3.0
psycopg2-binary>=2.9.0

# Avro processing for comprehensive tests
fastavro>=1.4.0

# Optional: pytest for structured testing
pytest>=7.0.0
pytest-postgresql>=4.0.0

# Optional: for more advanced testing
pytest-mock>=3.6.0
pytest-cov>=4.0.0
