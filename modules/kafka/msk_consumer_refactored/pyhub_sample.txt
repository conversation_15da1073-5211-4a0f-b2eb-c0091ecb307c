{"BATCH_ID": 300008711, "FILE_REF_NO": {"bytes": 300008711420386015}, "TXN_REF_NO": 420386015, "PYMNT_AMNT": {"bytes": 1000.0}, "PYMNT_VALUE_DATE": {"long": 1750982400000}, "PYMNT_CURR": {"string": "USD"}, "EX_RATE": {"bytes": 3.692}, "STATUS": {"string": "10"}, "DEBIT_ACC_NO": {"string": "**************"}, "ORDER_CUST_NAME": {"string": "**************AC DESC"}, "ORDER_CUST_ADDR1": {"string": "ADDRESS2"}, "ORDER_CUST_ADDR2": {"string": "ADDRESS3"}, "ORDER_CUST_ADDR3": {"string": "ADDRESS4"}, "BENE_NAME": {"string": "Mr <PERSON><PERSON>"}, "BENE_ACC_NO": {"string": "*****************"}, "BENE_ADDR1": {"string": "Time Square New York"}, "BENE_ADDR2": {"string": "/US/New York"}, "BENE_BANK_NAME": {"string": "Bank Of America"}, "REMITTANCE_INFO1": {"string": "USD Transfer"}, "CHARGE_TO_ID": {"string": "1"}, "TOTAL_CHARGE_AMOUNT": {"bytes": 63.0}, "OTHER_SYSTEM_REF_NO": {"string": "****************"}, "TRANSACTION_TYPE": {"string": "EFI"}, "PROCESS_DATETIME": {"long": *************}, "MAKER_DESC": {"string": "**********"}, "MAKER_DATE": {"long": *************}, "STATUS_DATETIME": {"long": ****************}, "CHECKER_DESC": {"string": "**********"}, "CHECKER_DATE": {"long": *************}, "MIDDLEWARE_REQUEST": {"string": "__debezium_unavailable_value"}, "MIDDLEWARE_RESPONSE": {"string": "__debezium_unavailable_value"}, "RECEIVING_CHANNEL_ID": {"string": "1"}, "PROCESSING_CHANNEL_ID": {"string": "3"}, "RECORD_TYPE": {"string": "2"}, "FORCE_DEBIT": {"string": "N"}, "FRAUD_FILTER_FLG": {"string": "Y"}, "EOSB_FILTER_FLG": {"string": "Y"}, "ORG_BRANCH_CODE_OLD": {"int": 0}, "DEST_BRANCH_CODE_OLD": {"int": 0}, "DEBIT_CCY": {"string": "AED"}, "PYMNT_DEBIT_RATE": {"bytes": 0.2708559}, "PYMNT_DEBIT_AMNT": {"bytes": 3692.0}, "PYMNT_AED_RATE": {"bytes": 3.692}, "PYMNT_AED_AMNT": {"bytes": 3692.0}, "DEBIT_AED_RATE": {"bytes": 1.0}, "DEBIT_AED_AMNT": {"bytes": 3692.0}, "CID": {"string": "********"}, "TXN_CONSOLIDATION_FLG": {"string": "N"}, "DUPLICATE_FLG": {"string": "Y"}, "IBAN_DR": {"string": "***********************"}, "EX_RATE_BUY": {"bytes": 1.0}, "TRANSACTION_CODE": {"string": "ATS"}, "MESSAGE_TYPE": {"string": "103"}, "INSTRUCTED_TRANSACTION_AMOUNT": {"bytes": 1000.0}, "ORDERING_INSTITUTION": {"string": "ADCBAEAA"}, "INSTRUCTED_CURRENCY": {"string": "USD"}, "SENDING_INSTITUTE_IDENTIFIER": {"string": "ADCBAEAA"}, "BANK_OPERATION_CODE": {"string": "UAEFTSRB"}, "MESSAGE": {"string": "__debezium_unavailable_value"}, "CUSTOMER_CATEGORY": {"string": "M"}, "CB_CHARGE": {"bytes": 0.0}, "BANK_BIC": {"string": "BOFAUS3NXXX"}, "BANK_CODE_TYPE": {"string": "/FW"}, "PROFIT_CENTRE": {"string": "561"}, "NOSTRO_BANK_BIC": {"string": "CITIUS33"}, "NOSTRO_ACC_NO": {"string": "**********"}, "BANK_COUNTRY": {"string": "US"}, "DOWNLOAD_DATE": {"long": *************}, "IS_JERSEY": {"string": "N"}, "RANDOM_FRAUD": {"string": "Y"}, "ORG_BRANCH_CODE": {"string": "049"}, "DEST_BRANCH_CODE": {"string": "049"}, "SERVICE_TYPE_IDENT": {"string": "001"}, "UETR_NO": {"string": "25594f43-11ae-49a7-842b-6d3c75e82c9e"}, "RECEIVE_DATETIME": {"long": ****************}, "PYMNT_STAGE": {"string": "In Progress"}, "__deleted": {"string": "false"}, "__op": {"string": "u"}, "__ts_ms": {"long": *************}}