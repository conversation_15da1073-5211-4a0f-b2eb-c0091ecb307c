import json
import logging
import time
from decimal import Decimal
from datetime import datetime
from typing import List, Dict, Any
from confluent_kafka import Producer
from postgres_utils import db_connection
from logging_utils import handle_discarded_message

# Configuration constants
ENABLE_DLQ = True

def get_dlq_topic_name(topic: str) -> str:
    """Generate DLQ topic name from original topic name."""
    return f"{topic}-DLQ"

class BytesEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles bytes, Decimal objects, and other non-serializable types."""
    def default(self, obj):
        if isinstance(obj, bytes):
            return obj.hex()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'hex'):
            return obj.hex()
        elif hasattr(obj, '__str__'):
            return str(obj)
        return super().default(obj)

def make_json_serializable(obj):
    """
    Recursively convert a dictionary with non-serializable values to a serializable format.
    """
    if isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(make_json_serializable(item) for item in obj)
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, bytes):
        return obj.hex()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif obj is None or isinstance(obj, (str, int, float, bool)):
        return obj
    else:
        return str(obj)

def adapt_value_for_postgres(value):
    """
    Convert Python values to PostgreSQL-compatible types.
    """
    from postgres_utils import decode_variable_scale_decimal

    original_type = type(value).__name__

    if isinstance(value, dict):
        if 'scale' in value and 'value' in value and isinstance(value['value'], bytes):
            try:
                adapted = decode_variable_scale_decimal(value)
                logging.debug(f"Adapted VariableScaleDecimal from {original_type} to {type(adapted).__name__}: {adapted}")
                return adapted
            except Exception as e:
                logging.error(f"Error adapting VariableScaleDecimal: {e}")
                return str(value)
        adapted = json.dumps(value, cls=BytesEncoder)
        logging.debug(f"Adapted dict from {original_type} to string: {adapted[:100]}...")
        return adapted
    elif isinstance(value, list):
        adapted = json.dumps(value, cls=BytesEncoder)
        logging.debug(f"Adapted list from {original_type} to string: {adapted[:100]}...")
        return adapted
    elif isinstance(value, bytes):
        adapted = value.hex()
        logging.debug(f"Adapted bytes from {original_type} to hex string: {adapted[:100]}...")
        return adapted
    elif isinstance(value, Decimal):
        adapted = float(value)
        logging.debug(f"Adapted Decimal from {original_type} to float: {adapted}")
        return adapted

    logging.debug(f"No adaptation needed for {original_type}: {str(value)[:100]}")
    return value

def send_to_dlq(raw_message: bytes, topic: str, producer: Producer) -> None:
    """Send a message to the Dead Letter Queue."""
    if not ENABLE_DLQ:
        logging.debug("DLQ is disabled, skipping")
        return

    dlq_topic = get_dlq_topic_name(topic)

    try:
        producer.produce(
            dlq_topic,
            value=raw_message,
            callback=lambda err, msg: logging.info(f"Raw message sent to DLQ: {msg.offset()}") if err is None else logging.error(f"Failed to send to DLQ: {err}")
        )
        logging.info(f"Sent raw message to DLQ topic {dlq_topic}")
    except Exception as e:
        logging.error(f"Error sending raw message to DLQ: {e}")

def insert_batch_into_postgres(batch: List[Dict], schema_name: str, table_name: str, table_columns: List[str],
                              topic: str, discarded_messages_file: str, dlq_producer: Producer = None) -> None:
    """
    Insert a batch of records into PostgreSQL with fallback to individual record processing.
    """
    if not batch:
        return

    failed_records = []
    successful_records = []

    try:
        all_values = []
        successful_indices = []
        preprocessing_failed_records = []

        # Prepare all records for batch insertion
        for idx, data in enumerate(batch):
            try:
                # Prepare values for this record
                values = []
                for col in table_columns:
                    if col in data:
                        adapted_value = adapt_value_for_postgres(data[col])
                        values.append(adapted_value)
                    else:
                        values.append(None)

                all_values.append(values)
                successful_indices.append(idx)
            except Exception as e:
                offset = data.get('_kafka_offset')
                preprocessing_failed_records.append((data, str(e)))
                logging.error(f"Failed to prepare record {idx} for batch at offset {offset}: {e}")

                if dlq_producer and '_raw_message' in data:
                    send_to_dlq(
                        raw_message=data['_raw_message'],
                        topic=topic,
                        producer=dlq_producer
                    )

                handle_discarded_message(
                    message=data,
                    reason=f"Batch preparation failed: {e}",
                    topic=topic,
                    offset=offset,
                    discarded_messages_file=discarded_messages_file
                )

        # Try batch insertion first
        if all_values:
            # Retry logic for primary key fetching and batch insertion
            max_pk_retries = 5
            pk_retry_delay = 2.0

            for pk_attempt in range(max_pk_retries):
                try:
                    from postgres_utils import get_primary_key, PrimaryKeyFetchError

                    # Attempt to get primary keys
                    try:
                        primary_keys = get_primary_key(schema_name, table_name)
                    except PrimaryKeyFetchError as pk_error:
                        if pk_attempt < max_pk_retries - 1:
                            retry_delay = pk_retry_delay * (2 ** pk_attempt)
                            logging.warning(f"Primary key fetch failed (attempt {pk_attempt + 1}/{max_pk_retries}): {pk_error}. Retrying in {retry_delay:.2f}s")
                            time.sleep(retry_delay)
                            continue
                        else:
                            # After all retries failed, re-raise the exception to fail the batch
                            logging.error(f"Primary key fetch failed after {max_pk_retries} attempts. Cannot proceed with batch insertion.")
                            raise pk_error

                    # Successfully got primary keys (could be empty list if table has no PKs)
                    columns_str = ", ".join(f'"{col}"' for col in table_columns)
                    placeholders = ", ".join(["%s"] * len(table_columns))

                    # Build ON CONFLICT clause based on primary keys and op_ts
                    if primary_keys:
                        pk_columns = ", ".join(f'"{pk}"' for pk in primary_keys)

                        # Check if op_ts column exists in the table
                        has_op_ts = 'op_ts' in table_columns

                        if has_op_ts:
                            # Use timestamp-based conflict resolution
                            update_columns = [col for col in table_columns if col not in primary_keys]
                            update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)

                            query = f'''
                            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
                            VALUES ({placeholders})
                            ON CONFLICT ({pk_columns}) DO UPDATE SET
                            {update_set}
                            WHERE "{schema_name}"."{table_name}".op_ts < EXCLUDED.op_ts
                            '''
                        else:
                            # Standard conflict resolution without timestamp check
                            update_columns = [col for col in table_columns if col not in primary_keys]
                            update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)

                            query = f'''
                            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
                            VALUES ({placeholders})
                            ON CONFLICT ({pk_columns}) DO UPDATE SET
                            {update_set}
                            '''
                    else:
                        # No primary keys found (successful query but empty result) - use basic INSERT
                        logging.info(f"No primary keys found for {schema_name}.{table_name}. Using basic INSERT without conflict resolution.")
                        query = f'''
                        INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
                        VALUES ({placeholders})
                        '''

                    # Execute the batch insertion
                    with db_connection() as conn:
                        with conn.cursor() as cursor:
                            cursor.executemany(query, all_values)
                        conn.commit()

                    successful_records = [batch[idx] for idx in successful_indices]
                    logging.info(f"✅ Successfully inserted batch of {len(successful_records)} records into {schema_name}.{table_name}")
                    return

                except Exception as batch_error:
                    logging.warning(f"Batch insertion failed for {schema_name}.{table_name}: {batch_error}")
                    logging.info("Falling back to individual record processing")
                    break  # Exit the retry loop and proceed to individual processing

        # Fallback to individual record processing
        successful_count = 0

        # Retry logic for primary key fetching in individual processing
        max_pk_retries = 5
        pk_retry_delay = 2.0
        primary_keys = None

        for pk_attempt in range(max_pk_retries):
            try:
                from postgres_utils import get_primary_key, PrimaryKeyFetchError
                primary_keys = get_primary_key(schema_name, table_name)
                break  # Successfully got primary keys, exit retry loop
            except PrimaryKeyFetchError as pk_error:
                if pk_attempt < max_pk_retries - 1:
                    retry_delay = pk_retry_delay * (2 ** pk_attempt)
                    logging.warning(f"Primary key fetch failed during individual processing (attempt {pk_attempt + 1}/{max_pk_retries}): {pk_error}. Retrying in {retry_delay:.2f}s")
                    time.sleep(retry_delay)
                    continue
                else:
                    # After all retries failed, fail the entire batch
                    logging.error(f"Primary key fetch failed after {max_pk_retries} attempts during individual processing. Cannot proceed.")
                    raise pk_error

        with db_connection() as conn:
            with conn.cursor() as cursor:
                columns_str = ", ".join(f'"{col}"' for col in table_columns)
                placeholders = ", ".join(["%s"] * len(table_columns))

                # Build ON CONFLICT clause for individual records (same logic as batch)
                if primary_keys:
                    pk_columns = ", ".join(f'"{pk}"' for pk in primary_keys)

                    # Check if op_ts column exists in the table
                    has_op_ts = 'op_ts' in table_columns

                    if has_op_ts:
                        # Use timestamp-based conflict resolution
                        update_columns = [col for col in table_columns if col not in primary_keys]
                        update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)

                        query = f'''
                        INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
                        VALUES ({placeholders})
                        ON CONFLICT ({pk_columns}) DO UPDATE SET
                        {update_set}
                        WHERE "{schema_name}"."{table_name}".op_ts < EXCLUDED.op_ts
                        '''
                    else:
                        # Standard conflict resolution without timestamp check
                        update_columns = [col for col in table_columns if col not in primary_keys]
                        update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)

                        query = f'''
                        INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
                        VALUES ({placeholders})
                        ON CONFLICT ({pk_columns}) DO UPDATE SET
                        {update_set}
                        '''
                else:
                    # No primary keys found (successful query but empty result) - use basic INSERT
                    logging.info(f"No primary keys found for {schema_name}.{table_name} during individual processing. Using basic INSERT without conflict resolution.")
                    query = f'''
                    INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
                    VALUES ({placeholders})
                    '''

                for idx, values in zip(successful_indices, all_values):
                    try:
                        cursor.execute(query, values)
                        conn.commit()
                        successful_records.append(batch[idx])
                        successful_count += 1
                    except Exception as record_error:
                        offset = batch[idx].get('_kafka_offset')
                        failed_records.append((batch[idx], str(record_error)))
                        logging.error(f"Failed to process record {idx} individually at offset {offset}: {record_error}")

                        if dlq_producer and '_raw_message' in batch[idx]:
                            send_to_dlq(
                                raw_message=batch[idx]['_raw_message'],
                                topic=topic,
                                producer=dlq_producer
                            )

                        handle_discarded_message(
                            message=batch[idx],
                            reason=f"Individual insert failed: {record_error}",
                            topic=topic,
                            offset=offset,
                            discarded_messages_file=discarded_messages_file
                        )

        logging.info(f"✅ Successfully processed {successful_count} individual records into {schema_name}.{table_name}")

    except Exception as e:
        remaining_records = [record for record in batch if record not in successful_records]
        logging.error(f"❌ Critical error during batch processing for {schema_name}.{table_name}: {str(e)}")
        logging.error(f"Number of unprocessed records due to critical error: {len(remaining_records)}")

        for record in remaining_records:
            offset = record.get('_kafka_offset')

            if dlq_producer and '_raw_message' in record:
                send_to_dlq(
                    raw_message=record['_raw_message'],
                    topic=topic,
                    producer=dlq_producer
                )

            handle_discarded_message(
                message=record,
                reason=f"Critical batch processing error: {e}",
                topic=topic,
                offset=offset,
                discarded_messages_file=discarded_messages_file
            )
