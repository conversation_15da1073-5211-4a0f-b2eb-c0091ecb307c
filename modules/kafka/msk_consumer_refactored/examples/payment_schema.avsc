{"type": "record", "name": "PaymentTransaction", "namespace": "com.example.payments", "fields": [{"name": "BATCH_ID", "type": "int"}, {"name": "FILE_REF_NO", "type": {"type": "bytes", "logicalType": "decimal", "precision": 20, "scale": 0}}, {"name": "TXN_REF_NO", "type": "long"}, {"name": "PYMNT_AMNT", "type": {"type": "bytes", "logicalType": "decimal", "precision": 15, "scale": 9}}, {"name": "PYMNT_VALUE_DATE", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "PYMNT_CURR", "type": "string"}, {"name": "EX_RATE", "type": {"type": "bytes", "logicalType": "decimal", "precision": 15, "scale": 10}}, {"name": "STATUS", "type": "string"}, {"name": "DEBIT_ACC_NO", "type": "string"}, {"name": "ORDER_CUST_NAME", "type": ["null", "string"], "default": null}, {"name": "BENE_NAME", "type": ["null", "string"], "default": null}, {"name": "BENE_ACC_NO", "type": ["null", "string"], "default": null}, {"name": "PROCESS_DATETIME", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "MAKER_DATE", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "CHECKER_DATE", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "TRANSACTION_TYPE", "type": "string"}, {"name": "DEBIT_CCY", "type": "string"}, {"name": "PYMNT_DEBIT_AMNT", "type": {"type": "bytes", "logicalType": "decimal", "precision": 15, "scale": 9}}, {"name": "CID", "type": ["null", "string"], "default": null}, {"name": "__op", "type": "string"}, {"name": "__ts_ms", "type": {"type": "long", "logicalType": "timestamp-millis"}}]}