CREATE TABLE test_schema.payment_transactions (
    batch_id INTEGER NOT NULL,
    file_ref_no NUMERIC(20,0),
    txn_ref_no BIGINT NOT NULL,
    pymnt_amnt NUMERIC(15,9) CHECK (pymnt_amnt >= 0),
    pymnt_value_date TIMESTAMP WITHOUT TIME ZONE,
    pymnt_curr VARCHAR(3) CHECK (LENGTH(pymnt_curr) = 3),
    ex_rate NUMERIC(15,10) CHECK (ex_rate > 0),
    status VARCHAR(10),
    debit_acc_no VARCHAR(50),
    order_cust_name VARCHAR(255),
    bene_name VARCHAR(255),
    bene_acc_no VARCHAR(50),
    process_datetime TIMESTAMP WITHOUT TIME ZONE,
    maker_date TIMESTAMP WITHOUT TIME ZONE,
    checker_date TIMESTAMP WITHOUT TIME ZONE,
    transaction_type VARCHAR(10),
    debit_ccy VARCHAR(3),
    pymnt_debit_amnt NUMERIC(15,9),
    cid VARCHAR(20),
    op_ts TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (batch_id, txn_ref_no),
    
    CONSTRAINT valid_currencies 
        CHECK (pymnt_curr ~ '^[A-Z]{3}$' AND debit_ccy ~ '^[A-Z]{3}$'),
    CONSTRAINT valid_status_values 
        CHECK (status IN ('10', '20', '30', '40', '50')),
    CONSTRAINT valid_transaction_types
        CHECK (transaction_type IN ('CREDIT', 'DEBIT', 'TRANSFER'))
);
