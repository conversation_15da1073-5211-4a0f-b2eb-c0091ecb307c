# Generic PostgreSQL Test Examples

This directory contains example files for testing the MSK consumer pipeline with different table schemas and data formats.

## Files

### Payment Transaction Example

- **`payment_schema.avsc`**: Avro schema for payment transaction messages
- **`payment_data.json`**: Sample payment transaction data in Avro union format
- **`payment_table.sql`**: PostgreSQL DDL for payment transactions table

### Usage

```bash
# Test payment transaction pipeline
python3 ../test_with_real_postgres.py \
    --value-schema payment_schema.avsc \
    --test-data payment_data.json \
    --ddl payment_table.sql \
    --table-name payment_transactions \
    --schema-name test_schema
```

## Creating Your Own Test Files

### 1. Avro Schema (.avsc)

Create an Avro schema file that matches your Kafka message structure:

```json
{
  "type": "record",
  "name": "YourRecord",
  "namespace": "com.example",
  "fields": [
    {
      "name": "field_name",
      "type": "string"
    },
    {
      "name": "decimal_field",
      "type": {
        "type": "bytes",
        "logicalType": "decimal",
        "precision": 15,
        "scale": 9
      }
    },
    {
      "name": "timestamp_field",
      "type": {
        "type": "long",
        "logicalType": "timestamp-millis"
      }
    }
  ]
}
```

### 2. Test Data (.json)

Create test data that matches your Avro schema. Use Avro union format for complex types:

```json
{
  "field_name": {"string": "test_value"},
  "decimal_field": {"bytes": 1000.123456789},
  "timestamp_field": {"long": 1750854821000}
}
```

### 3. DDL (.sql)

Create PostgreSQL DDL that defines your target table:

```sql
CREATE TABLE your_schema.your_table (
    field_name VARCHAR(255),
    decimal_field NUMERIC(15,9),
    timestamp_field TIMESTAMP WITHOUT TIME ZONE,
    
    PRIMARY KEY (field_name),
    
    CONSTRAINT your_constraint 
        CHECK (decimal_field >= 0)
);
```

## Data Type Mappings

| Avro Type | Avro Logical Type | PostgreSQL Type | Example |
|-----------|-------------------|-----------------|---------|
| `string` | - | `VARCHAR(n)` | `"USD"` |
| `int` | - | `INTEGER` | `12345` |
| `long` | - | `BIGINT` | `1234567890` |
| `bytes` | `decimal` | `NUMERIC(p,s)` | `1000.123456789` |
| `long` | `timestamp-millis` | `TIMESTAMP` | `1750854821000` |
| `long` | `timestamp-micros` | `TIMESTAMP` | `1750854821000000` |

## Test Data Formats

### Avro Union Format (Recommended)

```json
{
  "string_field": {"string": "value"},
  "decimal_field": {"bytes": 1000.50},
  "timestamp_field": {"long": 1750854821000}
}
```

### Direct Format (Also Supported)

```json
{
  "string_field": "value",
  "decimal_field": 1000.50,
  "timestamp_field": 1750854821000
}
```

## Running Tests

### Basic Test
```bash
python3 ../test_with_real_postgres.py \
    --value-schema your_schema.avsc \
    --test-data your_data.json \
    --ddl your_table.sql
```

### With Custom Names
```bash
python3 ../test_with_real_postgres.py \
    --value-schema your_schema.avsc \
    --test-data your_data.json \
    --ddl your_table.sql \
    --table-name custom_table \
    --schema-name custom_schema
```

### With Key Schema
```bash
python3 ../test_with_real_postgres.py \
    --key-schema key_schema.avsc \
    --value-schema value_schema.avsc \
    --test-data your_data.json \
    --ddl your_table.sql
```

## Expected Output

```
🧪 Generic MSK Consumer Test with Real PostgreSQL
==================================================
📄 Loading input files...
  ✅ Loaded Avro schema: your_schema.avsc
  ✅ Loaded test data: your_data.json
  ✅ Loaded DDL: your_table.sql
  📊 Target: your_schema.your_table

🔄 Processing Avro data...
  ✅ Processed 15 fields from test data
  📊 Schema validation: 12 matching, 3 extra, 0 missing

🔄 Starting temporary PostgreSQL instance...
  ✅ PostgreSQL started on postgresql://postgres@127.0.0.1:xxxxx/test
  📊 PostgreSQL version: PostgreSQL 14.18 (Homebrew)

🔄 Creating test table from DDL...
  ✅ Created schema: your_schema
  ✅ Created table from provided DDL
  📊 Table: your_schema.your_table
  📊 Columns: 15 columns discovered from database
  🔑 Primary keys: batch_id, txn_ref_no

🎯 Generic PostgreSQL Test: SUCCESS
```
