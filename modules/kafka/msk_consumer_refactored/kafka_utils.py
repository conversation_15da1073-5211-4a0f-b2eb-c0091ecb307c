import logging
from datetime import datetime
from confluent_kafka import Consumer, Producer
from confluent_kafka.admin import AdminClient, NewTopic
from postgres_utils import process_message, get_table_columns
from postgres_batch_utils import insert_batch_into_postgres
from config_loader import get_local_schema, fetch_schema_from_registry, set_process_name, get_discarded_messages_filename, setup_logging
from logging_utils import handle_discarded_message
from secrets_manager import get_kafka_credentials

KAFKA_BOOTSTRAP = get_kafka_credentials()["msk-bootstrap"]
ENABLE_DLQ = True
DLQ_SUFFIX = "-DLQ"

class KafkaConsumerService:
    def __init__(self, bootstrap_servers, schema_registry_client=None, error_handler=None):
        self.bootstrap_servers = bootstrap_servers
        self.schema_registry_client = schema_registry_client
        self.error_handler = error_handler

    def create_consumer(self, group_id, auto_offset_reset="earliest"):
        return Consumer({
            "bootstrap.servers": self.bootstrap_servers,
            "group.id": group_id,
            "auto.offset.reset": auto_offset_reset,
            "security.protocol": "PLAINTEXT",
            "enable.auto.commit": False
        })

    def create_dlq_producer(self):
        return Producer({
            "bootstrap.servers": self.bootstrap_servers,
            "security.protocol": "PLAINTEXT"
        })

def get_dlq_topic_name(source_topic: str) -> str:
    return f"{source_topic}{DLQ_SUFFIX}"

def send_to_dlq(raw_message: bytes, topic: str, producer: Producer) -> None:
    if not ENABLE_DLQ:
        return

    dlq_topic = get_dlq_topic_name(topic)
    try:
        producer.produce(
            dlq_topic,
            value=raw_message,
            callback=lambda err, msg: logging.info(f"Raw message sent to DLQ: {msg.offset()}") if err is None else logging.error(f"Failed to send to DLQ: {err}")
        )
        logging.info(f"Sent raw message to DLQ topic {dlq_topic}")
    except Exception as e:
        logging.error(f"Error sending raw message to DLQ: {e}")

def ensure_dlq_topic_exists(source_topic: str) -> None:
    if not ENABLE_DLQ:
        return

    dlq_topic = get_dlq_topic_name(source_topic)
    logging.info(f"Checking if DLQ topic '{dlq_topic}' exists...")

    try:
        admin_client = AdminClient({"bootstrap.servers": KAFKA_BOOTSTRAP, "security.protocol": "PLAINTEXT"})
        metadata = admin_client.list_topics(timeout=10)
        topics = metadata.topics

        if dlq_topic not in topics:
            topic_list = [NewTopic(
                dlq_topic,
                num_partitions=3,
                replication_factor=2,
                config={
                    "retention.ms": str(14 * 24 * 60 * 60 * 1000),
                    "cleanup.policy": "delete"
                }
            )]
            fs = admin_client.create_topics(topic_list)

            for topic, f in fs.items():
                try:
                    f.result()
                    logging.info(f"Topic {topic} created successfully")
                except Exception as e:
                    if "already exists" in str(e):
                        logging.info(f"Topic {topic} already exists")
                    else:
                        raise
        else:
            logging.info(f"DLQ topic '{dlq_topic}' already exists")

    except Exception as e:
        logging.error(f"Error ensuring DLQ topic exists: {e}")


def consume_messages(table_name: str, use_local_schema: bool, config: dict) -> None:
    if table_name not in config["table_mapping"]:
        logging.error(f"Table '{table_name}' not found in configuration.")
        return

    process_name = set_process_name(table_name, config)

    # Configure logging to use process name for log file
    log_level = config.get("log_level", "INFO")
    setup_logging(process_name, log_level)

    logging.info(f"Started consumer process: {process_name}")

    table_config = config["table_mapping"][table_name]
    topic = table_config["topic"]
    discarded_messages_file = get_discarded_messages_filename(table_name)

    dlq_producer = None
    if ENABLE_DLQ:
        dlq_producer = Producer({"bootstrap.servers": KAFKA_BOOTSTRAP, "security.protocol": "PLAINTEXT"})
        ensure_dlq_topic_exists(topic)

    schema_name, tbl = table_name.split(".")
    table_columns = get_table_columns(schema_name, tbl)

    if use_local_schema:
        local_schema_file = table_config.get("local_schema_file", "")
        if not local_schema_file:
            logging.error("Local schema file not provided in configuration while --use-local-schema flag is used.")
            return
        schema_dict = get_local_schema(local_schema_file)
        if not schema_dict:
            logging.error(f"Failed to load local schema from file {local_schema_file} for topic {topic}")
            return
    else:
        schema_dict = fetch_schema_from_registry(topic)
        if not schema_dict:
            logging.error(f"Failed to fetch schema for topic {topic}")
            return

    consumer = Consumer({
        "bootstrap.servers": KAFKA_BOOTSTRAP,
        "group.id": table_config.get("group_id", "data-platform"),
        "auto.offset.reset": "earliest",
        "security.protocol": "PLAINTEXT",
        "enable.auto.commit": False
    })
    consumer.subscribe([topic])
    batch, last_batch_time = [], datetime.now()
    BATCH_SIZE = table_config.get("write_batch_size", 500)
    BATCH_WAIT_TIME = table_config.get("batch_wait", 2.0)

    try:
        while True:
            msg = consumer.poll(timeout=1.0)
            if msg is None:
                if batch and (datetime.now() - last_batch_time).seconds >= BATCH_WAIT_TIME:
                    insert_batch_into_postgres(batch, schema_name, tbl, table_columns, topic, discarded_messages_file, dlq_producer)
                    consumer.commit()
                    batch.clear()
                    last_batch_time = datetime.now()
                continue

            if msg.error():
                logging.error(f"Kafka error at offset {msg.offset()}: {msg.error()}")
                continue

            try:
                payload = msg.value()[5:]
                decoded_message = process_message(payload, schema_dict)
                decoded_message['_kafka_offset'] = msg.offset()
                decoded_message['_raw_message'] = msg.value()
                batch.append(decoded_message)
            except Exception as decode_error:
                handle_discarded_message({"raw": msg.value().hex()}, f"Decoding error: {decode_error}", topic, msg.offset(), discarded_messages_file)
                if dlq_producer:
                    send_to_dlq(msg.value(), topic, dlq_producer)

            if len(batch) >= BATCH_SIZE:
                insert_batch_into_postgres(batch, schema_name, tbl, table_columns, topic, discarded_messages_file, dlq_producer)
                consumer.commit()
                batch.clear()
                last_batch_time = datetime.now()

    except KeyboardInterrupt:
        logging.info("Consumer interrupted.")
    finally:
        if batch:
            insert_batch_into_postgres(batch, schema_name, tbl, table_columns, topic, discarded_messages_file, dlq_producer)
            consumer.commit()
        consumer.close()
        if dlq_producer:
            dlq_producer.flush()
