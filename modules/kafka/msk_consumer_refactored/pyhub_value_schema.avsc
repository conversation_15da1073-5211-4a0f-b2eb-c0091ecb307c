{"type": "record", "name": "Value", "namespace": "PYMTHUB.ADLY011.PAYMENT_TXN_MT", "fields": [{"name": "BATCH_ID", "type": {"type": "bytes", "scale": 0, "precision": 25, "connect.version": 1, "connect.parameters": {"scale": "0", "connect.decimal.precision": "25"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}}, {"name": "FILE_REF_NO", "type": ["null", {"type": "bytes", "scale": 0, "precision": 38, "connect.version": 1, "connect.parameters": {"scale": "0", "connect.decimal.precision": "38"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "TXN_REF_NO", "type": {"type": "bytes", "scale": 0, "precision": 20, "connect.version": 1, "connect.parameters": {"scale": "0", "connect.decimal.precision": "20"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}}, {"name": "PYMNT_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_VALUE_DATE", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.Timestamp"}], "default": null}, {"name": "PYMNT_CURR", "type": ["null", "string"], "default": null}, {"name": "EX_RATE", "type": ["null", {"type": "bytes", "scale": 10, "precision": 22, "connect.version": 1, "connect.parameters": {"scale": "10", "connect.decimal.precision": "22"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "STATUS", "type": ["null", "string"], "default": null}, {"name": "ERROR_CODE", "type": ["null", "string"], "default": null}, {"name": "ERROR_DESC", "type": ["null", "string"], "default": null}, {"name": "DEBIT_ACC_NO", "type": ["null", "string"], "default": null}, {"name": "DEBIT_ACC_BIC", "type": ["null", "string"], "default": null}, {"name": "ORDER_CUST_NAME", "type": ["null", "string"], "default": null}, {"name": "ORDER_CUST_ADDR1", "type": ["null", "string"], "default": null}, {"name": "ORDER_CUST_ADDR2", "type": ["null", "string"], "default": null}, {"name": "ORDER_CUST_ADDR3", "type": ["null", "string"], "default": null}, {"name": "BENE_NAME", "type": ["null", "string"], "default": null}, {"name": "BENE_ACC_NO", "type": ["null", "string"], "default": null}, {"name": "BENE_ADDR1", "type": ["null", "string"], "default": null}, {"name": "BENE_ADDR2", "type": ["null", "string"], "default": null}, {"name": "BENE_ADDR3", "type": ["null", "string"], "default": null}, {"name": "BENE_ACC_BIC", "type": ["null", "string"], "default": null}, {"name": "BENE_BANK_NAME", "type": ["null", "string"], "default": null}, {"name": "BENE_BANK_ADDR1", "type": ["null", "string"], "default": null}, {"name": "BENE_BANK_ADDR2", "type": ["null", "string"], "default": null}, {"name": "BENE_BANK_ADDR3", "type": ["null", "string"], "default": null}, {"name": "INTERMEDIARY_BANK_NAME", "type": ["null", "string"], "default": null}, {"name": "INTERMEDIARY_BANK_ADDRESS_1", "type": ["null", "string"], "default": null}, {"name": "INTERMEDIARY_BANK_ADDRESS_2", "type": ["null", "string"], "default": null}, {"name": "INTERMEDIARY_BANK_ADDRESS_3", "type": ["null", "string"], "default": null}, {"name": "REMITTANCE_INFO1", "type": ["null", "string"], "default": null}, {"name": "REMITTANCE_INFO2", "type": ["null", "string"], "default": null}, {"name": "REMITTANCE_INFO3", "type": ["null", "string"], "default": null}, {"name": "REMITTANCE_INFO4", "type": ["null", "string"], "default": null}, {"name": "CHARGE_TO_ID", "type": ["null", "string"], "default": null}, {"name": "TOTAL_CHARGE_AMOUNT", "type": [{"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.default": "\u0000", "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}, "null"], "default": "\u0000"}, {"name": "OTHER_SYSTEM_REF_NO", "type": ["null", "string"], "default": null}, {"name": "TRANSACTION_TYPE", "type": ["null", "string"], "default": null}, {"name": "PROCESS_DATETIME", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.Timestamp"}], "default": null}, {"name": "BRANCH_ROUTING_CODE", "type": ["null", "string"], "default": null}, {"name": "MAKER_DESC", "type": ["null", "string"], "default": null}, {"name": "MAKER_DATE", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.Timestamp"}], "default": null}, {"name": "STATUS_DATETIME", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.MicroTimestamp"}], "default": null}, {"name": "CHECKER_DESC", "type": ["null", "string"], "default": null}, {"name": "CHECKER_DATE", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.Timestamp"}], "default": null}, {"name": "MIDDLEWARE_REQUEST", "type": ["null", "string"], "default": null}, {"name": "MIDDLEWARE_RESPONSE", "type": ["null", "string"], "default": null}, {"name": "AUTHORIZE_YN", "type": ["null", "string"], "default": null}, {"name": "RECEIVING_CHANNEL_ID", "type": ["null", "string"], "default": null}, {"name": "FILE_NAME", "type": ["null", "string"], "default": null}, {"name": "POSTING_STATUS", "type": ["null", "string"], "default": null}, {"name": "PROCESSING_CHANNEL_ID", "type": ["null", "string"], "default": null}, {"name": "RECORD_TYPE", "type": ["null", "string"], "default": null}, {"name": "INTERMEDIARY_BANK_BIC", "type": ["null", "string"], "default": null}, {"name": "BENE_BANK_ID", "type": ["null", "string"], "default": null}, {"name": "FORCE_DEBIT", "type": [{"type": "string", "connect.default": "N"}, "null"], "default": "N"}, {"name": "FRAUD_FILTER_FLG", "type": [{"type": "string", "connect.default": "Y"}, "null"], "default": "Y"}, {"name": "BANK_REF_NO", "type": ["null", "string"], "default": null}, {"name": "EOSB_FILTER_FLG", "type": [{"type": "string", "connect.default": "Y"}, "null"], "default": "Y"}, {"name": "DR_NARRATIVE", "type": ["null", "string"], "default": null}, {"name": "CR_NARRATIVE", "type": ["null", "string"], "default": null}, {"name": "ORG_BRANCH_CODE_OLD", "type": [{"type": "int", "connect.default": 0}, "null"], "default": 0}, {"name": "DEST_BRANCH_CODE_OLD", "type": [{"type": "int", "connect.default": 0}, "null"], "default": 0}, {"name": "ORIGINAL_BATCH_ID", "type": ["null", "string"], "default": null}, {"name": "ORIGINAL_TXN_REF_NO", "type": ["null", "string"], "default": null}, {"name": "RECEIVING_BRANCH_CODE", "type": ["null", "string"], "default": null}, {"name": "DEBIT_CCY", "type": ["null", "string"], "default": null}, {"name": "DEBIT_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "BENE_CCY", "type": ["null", "string"], "default": null}, {"name": "BENE_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_DEBIT_RATE", "type": ["null", {"type": "bytes", "scale": 10, "precision": 22, "connect.version": 1, "connect.parameters": {"scale": "10", "connect.decimal.precision": "22"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_DEBIT_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_BENE_RATE", "type": ["null", {"type": "bytes", "scale": 7, "precision": 19, "connect.version": 1, "connect.parameters": {"scale": "7", "connect.decimal.precision": "19"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_BENE_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_AED_RATE", "type": ["null", {"type": "bytes", "scale": 10, "precision": 22, "connect.version": 1, "connect.parameters": {"scale": "10", "connect.decimal.precision": "22"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "PYMNT_AED_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "DEBIT_AED_RATE", "type": ["null", {"type": "bytes", "scale": 10, "precision": 22, "connect.version": 1, "connect.parameters": {"scale": "10", "connect.decimal.precision": "22"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "DEBIT_AED_AMNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "CID", "type": ["null", "string"], "default": null}, {"name": "CMS_BATCH_ID", "type": ["null", "string"], "default": null}, {"name": "CMS_TXN_REF_NO", "type": ["null", "string"], "default": null}, {"name": "CORPORATE_REF_NO", "type": ["null", "string"], "default": null}, {"name": "TXN_CONSOLIDATION_FLG", "type": ["null", "string"], "default": null}, {"name": "INSTRUMENT_NO", "type": ["null", "string"], "default": null}, {"name": "CHEQUE_NO", "type": ["null", "string"], "default": null}, {"name": "PAYMENT_CHANGRES_FLAG", "type": ["null", "string"], "default": null}, {"name": "SALARY_PAYMENT", "type": ["null", "string"], "default": null}, {"name": "CMS_FILE_NAME", "type": ["null", "string"], "default": null}, {"name": "FILE_CREATION_DATE", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.Timestamp"}], "default": null}, {"name": "DUPLICATE_FLG", "type": [{"type": "string", "connect.default": "N"}, "null"], "default": "N"}, {"name": "CMSBATCHNO", "type": ["null", "string"], "default": null}, {"name": "ISSUER_CODE", "type": ["null", "string"], "default": null}, {"name": "IBAN", "type": ["null", "string"], "default": null}, {"name": "IBAN_DR", "type": ["null", "string"], "default": null}, {"name": "DEALER_REF_NO", "type": ["null", "string"], "default": null}, {"name": "EX_RATE_BUY", "type": ["null", {"type": "bytes", "scale": 10, "precision": 22, "connect.version": 1, "connect.parameters": {"scale": "10", "connect.decimal.precision": "22"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "BANK_SORT_CODE", "type": ["null", "string"], "default": null}, {"name": "INTER_BANK_SORT_CODE", "type": ["null", "string"], "default": null}, {"name": "TRANSACTION_CODE", "type": ["null", "string"], "default": null}, {"name": "REMIT_TRANSACTION_DETAIL", "type": ["null", "string"], "default": null}, {"name": "BENE_BANK_BRANCH", "type": ["null", "string"], "default": null}, {"name": "INTERMED_BANK_BRANCH", "type": ["null", "string"], "default": null}, {"name": "ORDERING_CUST_CODE", "type": ["null", "string"], "default": null}, {"name": "USER_REF_NO", "type": ["null", "string"], "default": null}, {"name": "MESSAGE_TYPE", "type": ["null", "string"], "default": null}, {"name": "SEQUENCE_NUMBER", "type": ["null", "string"], "default": null}, {"name": "INSTRUCTED_TRANSACTION_AMOUNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "ORDERING_INSTITUTION", "type": ["null", "string"], "default": null}, {"name": "SENDER_CHARGE", "type": ["null", "string"], "default": null}, {"name": "SPECIAL_INST_FOR_BENE_BANK", "type": ["null", "string"], "default": null}, {"name": "ACCT_WITH_INSTITUTION", "type": ["null", "string"], "default": null}, {"name": "INSTRUCTED_CURRENCY", "type": ["null", "string"], "default": null}, {"name": "SENDERS_CORRESP", "type": ["null", "string"], "default": null}, {"name": "RECEIVERS_CORRESP", "type": ["null", "string"], "default": null}, {"name": "RELATED_REF_NO", "type": ["null", "string"], "default": null}, {"name": "ADD_TXN_DETAIL", "type": ["null", "string"], "default": null}, {"name": "P2P1_INFO", "type": ["null", "string"], "default": null}, {"name": "P2P2_INFO", "type": ["null", "string"], "default": null}, {"name": "P2P3_INFO", "type": ["null", "string"], "default": null}, {"name": "P2P4_INFO", "type": ["null", "string"], "default": null}, {"name": "P2P5_INFO", "type": ["null", "string"], "default": null}, {"name": "SENDING_INSTITUTE_IDENTIFIER", "type": ["null", "string"], "default": null}, {"name": "P2P_INFO1", "type": ["null", "string"], "default": null}, {"name": "ULTIMATE_BENE_DETAILS", "type": ["null", "string"], "default": null}, {"name": "C2B_INFO", "type": ["null", "string"], "default": null}, {"name": "BANK_OPERATION_CODE", "type": ["null", "string"], "default": null}, {"name": "PURPOSE_CODE", "type": ["null", "string"], "default": null}, {"name": "MESSAGE", "type": ["null", "string"], "default": null}, {"name": "BENIFICIARYADDRESS2", "type": ["null", "string"], "default": null}, {"name": "BENIFICIARYADDRESS3", "type": ["null", "string"], "default": null}, {"name": "CUSTOMER_CATEGORY", "type": ["null", "string"], "default": null}, {"name": "CB_CHARGE", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "CUSTOMER_TYPE", "type": ["null", "string"], "default": null}, {"name": "BENEFICIARY_ID", "type": ["null", "string"], "default": null}, {"name": "AUTHORIZATION_TYPE", "type": ["null", "string"], "default": null}, {"name": "UNDERLYING_ACCT_WITH_INST", "type": ["null", "string"], "default": null}, {"name": "P2P6_INFO", "type": ["null", "string"], "default": null}, {"name": "BANK_BIC", "type": ["null", "string"], "default": null}, {"name": "BANK_CODE", "type": ["null", "string"], "default": null}, {"name": "BANK_CODE_TYPE", "type": ["null", "string"], "default": null}, {"name": "PROFIT_CENTRE", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_BANK_BIC", "type": ["null", "string"], "default": null}, {"name": "FCY_ACCOUNT_TYPE", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_ACC_NO", "type": ["null", "string"], "default": null}, {"name": "BANK_COUNTRY", "type": ["null", "string"], "default": null}, {"name": "CUSTCOUNTRYCODE", "type": ["null", "string"], "default": null}, {"name": "DOWNLOAD_DATE", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.Timestamp"}], "default": null}, {"name": "BENEFICIARY_TYPE", "type": ["null", "string"], "default": null}, {"name": "CHARGEFAILSTATUS", "type": ["null", "string"], "default": null}, {"name": "VALUE_DATE_FLG", "type": ["null", "string"], "default": null}, {"name": "IS_JERSEY", "type": ["null", "string"], "default": null}, {"name": "UAEFTSREFNO", "type": ["null", "string"], "default": null}, {"name": "EIGHTDIGIT_ACCOUNT", "type": ["null", "string"], "default": null}, {"name": "CCM_FILE_REF", "type": ["null", {"type": "bytes", "scale": 0, "precision": 25, "connect.version": 1, "connect.parameters": {"scale": "0", "connect.decimal.precision": "25"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "CCM_SEND_INST_REF", "type": ["null", "string"], "default": null}, {"name": "RANDOM_FRAUD", "type": [{"type": "string", "connect.default": "Y"}, "null"], "default": "Y"}, {"name": "CCC_PYMNT_REF", "type": ["null", "string"], "default": null}, {"name": "RECEIVING_INST_IDENTFIER", "type": ["null", "string"], "default": null}, {"name": "CUSTMR_OWNERSHP_CODE", "type": ["null", "string"], "default": null}, {"name": "CUSTMR_IDENTF_CODE", "type": ["null", "string"], "default": null}, {"name": "ECONOMIC_ACT_CODE", "type": ["null", "string"], "default": null}, {"name": "BENE_CUSTOMER_TYPE", "type": ["null", "string"], "default": null}, {"name": "BENE_CID", "type": ["null", "string"], "default": null}, {"name": "AMOUNT_MODIFIED", "type": ["null", "string"], "default": null}, {"name": "INSTCT_AED_MNT", "type": ["null", {"type": "bytes", "scale": 9, "precision": 21, "connect.version": 1, "connect.parameters": {"scale": "9", "connect.decimal.precision": "21"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "FLD36_EX_RATE", "type": ["null", {"type": "bytes", "scale": 10, "precision": 22, "connect.version": 1, "connect.parameters": {"scale": "10", "connect.decimal.precision": "22"}, "connect.name": "org.apache.kafka.connect.data.Decimal", "logicalType": "decimal"}], "default": null}, {"name": "IS_CTD05_REPAIRED", "type": ["null", "string"], "default": null}, {"name": "ORG_BRANCH_CODE", "type": ["null", "string"], "default": null}, {"name": "DEST_BRANCH_CODE", "type": ["null", "string"], "default": null}, {"name": "TXN_ORG_BRANCH", "type": ["null", "string"], "default": null}, {"name": "SERVICE_TYPE_IDENT", "type": ["null", "string"], "default": null}, {"name": "SENDERS_CORR_ACCOUNT", "type": ["null", "string"], "default": null}, {"name": "REGULATORYREPORTING1", "type": ["null", "string"], "default": null}, {"name": "REGULATORYREPORTING2", "type": ["null", "string"], "default": null}, {"name": "REGULATORYREPORTING3", "type": ["null", "string"], "default": null}, {"name": "UETR_NO", "type": ["null", "string"], "default": null}, {"name": "RETRY_COUNT", "type": ["null", "string"], "default": null}, {"name": "GITU_DOMESTIC_TXN_PICKED_FLG", "type": ["null", "string"], "default": null}, {"name": "HOLD_NUMBER", "type": ["null", "string"], "default": null}, {"name": "WMQIN_RESPONSE_STATUS", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_IS210REQ", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_TXN_TYPE", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_INTERMEDIARY_INFO", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_BENE_INST", "type": ["null", "string"], "default": null}, {"name": "NOSTRO_DEBIT_ACC_BIC", "type": ["null", "string"], "default": null}, {"name": "ORDER_CUST_ACC", "type": ["null", "string"], "default": null}, {"name": "BENE_CNTRY_POP", "type": ["null", "string"], "default": null}, {"name": "CHARGE_DEDUCT", "type": ["null", "string"], "default": null}, {"name": "CHANNEL_IDENTIFIER", "type": ["null", "string"], "default": null}, {"name": "CHANNEL_REFERENCENO", "type": ["null", "string"], "default": null}, {"name": "TRANSFER_TYPE", "type": ["null", "string"], "default": null}, {"name": "RECEIVE_DATETIME", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "io.debezium.time.MicroTimestamp"}], "default": null}, {"name": "PYMNT_STAGE", "type": ["null", "string"], "default": null}, {"name": "REMITTANCE_INFO1_1", "type": ["null", "string"], "default": null}, {"name": "DEBIT_ACCT_CLASSIFICATION", "type": ["null", "string"], "default": null}, {"name": "PHY_ACC_NO", "type": ["null", "string"], "default": null}, {"name": "PHY_ACC_TITLE", "type": ["null", "string"], "default": null}, {"name": "INSTR_AMT_FLAG", "type": ["null", "string"], "default": null}, {"name": "INTERMEDIARY_INSTITUTION", "type": ["null", "string"], "default": null}, {"name": "IS_TECH_FAIL", "type": ["null", "string"], "default": null}, {"name": "IS_STP", "type": ["null", "string"], "default": null}, {"name": "CHARGE_PROCESSING_CHANNEL", "type": ["null", "string"], "default": null}, {"name": "__deleted", "type": ["null", "string"], "default": null}, {"name": "__op", "type": ["null", "string"], "default": null}, {"name": "__ts_ms", "type": ["null", "long"], "default": null}], "connect.name": "PYMTHUB.ADLY011.PAYMENT_TXN_MT.Value"}