{"poll_config": {"max_poll_interval_ms": 300000, "max_poll_records": 500, "poll_timeout_ms": 1000, "min_batch_size": 100, "max_batch_wait_ms": 5000}, "table_mapping": {"fcubs_kafka21.actb_history": {"topic": "FCUBS.ADFCUBSGC.ACTB_HISTORY", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_branch": {"topic": "FCUBS.ADFCUBSGC.STTM_BRANCH", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.cstm_function_userdef_fields": {"topic": "FCUBS.ADFCUBSGC.CSTM_FUNCTION_USERDEF_FIELDS", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_cust_personal": {"topic": "FCUBS.ADFCUBSGC.STTM_CUST_PERSONAL", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_ac_linked_entities": {"topic": "FCUBS.ADFCUBSGC.STTM_AC_LINKED_ENTITIES", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sitb_contract_master_custom": {"topic": "FCUBS.ADFCUBSGC.SITB_CONTRACT_MASTER_CUSTOM", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_cust_profession_custom": {"topic": "FCUBS.ADFCUBSGC.STTM_CUST_PROFESSION_CUSTOM", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttb_account": {"topic": "FCUBS.ADFCUBSGC.STTB_ACCOUNT", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.ictm_acc": {"topic": "FCUBS.ADFCUBSGC.ICTM_ACC", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_acc_joint_holder": {"topic": "FCUBS.ADFCUBSGC.STTM_ACC_JOINT_HOLDER", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.istb_contractis": {"topic": "FCUBS.ADFCUBSGC.ISTB_CONTRACTIS", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sitb_instruction": {"topic": "FCUBS.ADFCUBSGC.SITB_INSTRUCTION", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_cust_professional": {"topic": "FCUBS.ADFCUBSGC.STTM_CUST_PROFESSIONAL", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_cust_domestic": {"topic": "FCUBS.ADFCUBSGC.STTM_CUST_DOMESTIC", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_cust_account": {"topic": "FCUBS.ADFCUBSGC.STTM_CUST_ACCOUNT", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_cust_personal_custom": {"topic": "FCUBS.ADFCUBSGC.STTM_CUST_PERSONAL_CUSTOM", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.actb_daily_log": {"topic": "FCUBS.ADFCUBSGC.ACTB_DAILY_LOG", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_trn_code": {"topic": "FCUBS.ADFCUBSGC.STTM_TRN_CODE", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_collateral_lmcust": {"topic": "FCUBS.ADFCUBSGC.STTM_COLLATERAL_LMCUST", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_customer": {"topic": "FCUBS.ADFCUBSGC.STTM_CUSTOMER", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_customer_custom": {"topic": "FCUBS.ADFCUBSGC.STTM_CUSTOMER_CUSTOM", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sitb_cycle_detail": {"topic": "FCUBS.ADFCUBSGC.SITB_CYCLE_DETAIL", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.mitm_customer_default": {"topic": "FCUBS.ADFCUBSGC.MITM_CUSTOMER_DEFAULT", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sitb_contract_master": {"topic": "FCUBS.ADFCUBSGC.SITB_CONTRACT_MASTER", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}, "fcubs_kafka21.sttm_account_class": {"topic": "FCUBS.ADFCUBSGC.STTM_ACCOUNT_CLASS", "group-id": "data-platform", "write_batch_size": 500, "batch_wait": 0.5}, "pyhub_kafka21.payment_txn_mt": {"topic": "PYMTHUB.ADLY011.PAYMENT_TXN_MT", "group-id": "data-platform", "write_batch_size": 1000, "batch_wait": 1}}}