../../../bin/fastavro,sha256=FIfL68-JJ75DvexlL5C0P6DXAh5FyzgM0fQ3-Sv4Y7s,314
fastavro-1.11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fastavro-1.11.1.dist-info/METADATA,sha256=Y6seP0-cuxNpgY9SO4Sy_g_PlViwQH8qgGEIOVw0lBo,5740
fastavro-1.11.1.dist-info/RECORD,,
fastavro-1.11.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fastavro-1.11.1.dist-info/WHEEL,sha256=ndSQSyKfEb4-QYoSqR9pmJeIy6hpNbwBzv_4vZGKDVo,115
fastavro-1.11.1.dist-info/entry_points.txt,sha256=XQaWQLv7IHj9T8kSKP4SXWU0sCRTB6PUkzpdmfvE8-c,52
fastavro-1.11.1.dist-info/licenses/LICENSE,sha256=jEdt07ehRVBPf0RBRWyN1oHU9aMPY2qBtwtJ5C9YWUw,1068
fastavro-1.11.1.dist-info/licenses/NOTICE.txt,sha256=v7qF-HeSB0tlkyWstRJj1pPJ216A6rRKw7i0OqJXeck,563
fastavro-1.11.1.dist-info/top_level.txt,sha256=kcA1c2BP6SFQN2GFk9kbKob65w70HdXnETe8FlMzxd8,9
fastavro/__init__.py,sha256=q1kmnbTrL_OtTREeJqnGXqA1g4Ui0vBbLCRmUWhRNVU,1751
fastavro/__main__.py,sha256=m9j6fKiljIzgSUjus5j1Oqj7nHFhSdnHDidC6YNZg04,2423
fastavro/__pycache__/__init__.cpython-313.pyc,,
fastavro/__pycache__/__main__.cpython-313.pyc,,
fastavro/__pycache__/_logical_readers_py.cpython-313.pyc,,
fastavro/__pycache__/_logical_writers_py.cpython-313.pyc,,
fastavro/__pycache__/_read_common.cpython-313.pyc,,
fastavro/__pycache__/_read_py.cpython-313.pyc,,
fastavro/__pycache__/_schema_common.cpython-313.pyc,,
fastavro/__pycache__/_schema_py.cpython-313.pyc,,
fastavro/__pycache__/_validate_common.cpython-313.pyc,,
fastavro/__pycache__/_validation_py.cpython-313.pyc,,
fastavro/__pycache__/_write_common.cpython-313.pyc,,
fastavro/__pycache__/_write_py.cpython-313.pyc,,
fastavro/__pycache__/const.cpython-313.pyc,,
fastavro/__pycache__/json_read.cpython-313.pyc,,
fastavro/__pycache__/json_write.cpython-313.pyc,,
fastavro/__pycache__/logical_readers.cpython-313.pyc,,
fastavro/__pycache__/logical_writers.cpython-313.pyc,,
fastavro/__pycache__/read.cpython-313.pyc,,
fastavro/__pycache__/schema.cpython-313.pyc,,
fastavro/__pycache__/types.cpython-313.pyc,,
fastavro/__pycache__/utils.cpython-313.pyc,,
fastavro/__pycache__/validation.cpython-313.pyc,,
fastavro/__pycache__/write.cpython-313.pyc,,
fastavro/_logical_readers.cpython-313-darwin.so,sha256=x8NknMHyOniw_hQklZ1L1jzPNem4HzA10HyEzsXXv1o,183656
fastavro/_logical_readers.pyi,sha256=L4yu5hVdVMcFU7IiGbgLI_ffWCz52E-RTswZBp1oQcs,131
fastavro/_logical_readers_py.py,sha256=7KzabtxrbgnpahTDPoZq5eD8eow5lxqBS2WoFmI0B4k,2786
fastavro/_logical_readers_py.pyi,sha256=L4yu5hVdVMcFU7IiGbgLI_ffWCz52E-RTswZBp1oQcs,131
fastavro/_logical_writers.cpython-313-darwin.so,sha256=g4YxPX2C-RBwEtct81YOr_FN3GYeDsYwOkaXT6kNkb8,287592
fastavro/_logical_writers.pyi,sha256=vo85IeZZLXDg-oy85KTVZmPgITOg3T0PQ7JhB1dEYIE,95
fastavro/_logical_writers_py.py,sha256=lVb8PEgVXc8zovrqoATM394ZavrreJh8BejPhhy6asQ,7803
fastavro/_logical_writers_py.pyi,sha256=vo85IeZZLXDg-oy85KTVZmPgITOg3T0PQ7JhB1dEYIE,95
fastavro/_read.cpython-313-darwin.so,sha256=JfsxmjoxniY36H-3zwGuIJjkJrMELD8DSiQqv0NkGB0,662240
fastavro/_read.pyi,sha256=yHIPif68Sxs7E2XnGu_4shU-KGzyhQie3f-1MuWqA9Q,2323
fastavro/_read_common.py,sha256=4sMjIRIo1Nck1peDcFTGuXQcrkC2HXA95mPlyQwRuJo,758
fastavro/_read_py.py,sha256=GHAaHKPhvT0vmRTLPZEj2CBEk6kvYG1NsmALDAQbuXM,39134
fastavro/_schema.cpython-313-darwin.so,sha256=QJctZ4Ry4yJt2LNI72rUtG-tN4MSzcxXix1UGfXRRR4,428432
fastavro/_schema.pyi,sha256=LgVD5jvI_PjHRtLVhKgiNuZijHbxtsQcIVs4fdI8GxQ,950
fastavro/_schema_common.py,sha256=Y04AFgsMrR7gCuw6O5LvhRauL_A2NeaXjVmIija-Wws,1421
fastavro/_schema_py.py,sha256=z4m57EzgSOO7NzdSGzUhnMVhmx_jczKXIctvumiTZUU,33315
fastavro/_validate_common.py,sha256=fkLkbku34fBFuvKo8ARokKTa9A-PRoiQiJaJB5gibYk,633
fastavro/_validation.cpython-313-darwin.so,sha256=IVaWA0cDfXtmaiaUuvkBNN8YkJt4c02qDs0w3uscgmg,237272
fastavro/_validation.pyi,sha256=72-4SW-YJOhvusZGs_7rf_fzG9Ul9qAQ5NUiQS0urVw,338
fastavro/_validation_py.py,sha256=k5Dn_6qhPg4wcb8OHiRKseWSBeInGN3FnXdu8HCRgx4,10757
fastavro/_write.cpython-313-darwin.so,sha256=_os8DA-3bSr2cnCidM8gjnSp10_fAhpgwuLMpmLbmS8,778672
fastavro/_write.pyi,sha256=6SWBwt8rRI62bH-GgggPgIu08jh73ehVVWvNoztDqmA,1680
fastavro/_write_common.py,sha256=6LVrDcLy7juQ2cZUYtTMBvpL1cpZ1gkwr0xViEfEveo,619
fastavro/_write_py.py,sha256=enzw_bf3XprGtX8dxcV_8rGZyGqeJUNiNjuHhI-88dc,27478
fastavro/const.py,sha256=Z0u-jlNB0MVhKifgT6I1nHozJyArdDa83BZc7rZyNgQ,853
fastavro/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fastavro/io/__pycache__/__init__.cpython-313.pyc,,
fastavro/io/__pycache__/binary_decoder.cpython-313.pyc,,
fastavro/io/__pycache__/binary_encoder.cpython-313.pyc,,
fastavro/io/__pycache__/json_decoder.cpython-313.pyc,,
fastavro/io/__pycache__/json_encoder.cpython-313.pyc,,
fastavro/io/__pycache__/parser.cpython-313.pyc,,
fastavro/io/__pycache__/symbols.cpython-313.pyc,,
fastavro/io/binary_decoder.py,sha256=5dLSeuO-9LIWGcPqvZ-FGWP4mAt0YMHNSCpxOhMB0lM,4374
fastavro/io/binary_encoder.py,sha256=bqzRS8CxgoGyDMynnOjSVJIv095wzyu71MarHJlXytw,1861
fastavro/io/json_decoder.py,sha256=09j3aI-et3kAg41XlpMcCSeWZsq8p-Z379jCUwhauuU,6936
fastavro/io/json_encoder.py,sha256=XEVLk18y1MMOj6ZChSUT2XhH5QAKPsvArkInCdeadRM,5674
fastavro/io/parser.py,sha256=HQzSRnJLYt1IQRcQ1DTyEI0h6l_giq86C4LXTIrwEE0,5880
fastavro/io/symbols.py,sha256=widVc90aESieTEkgot77Gg_nbpMsgnjgASXSQfU7b1w,2321
fastavro/json_read.py,sha256=OFfXinxtG18w4zLWJZY2Pv9srMdsACmhUTN70HD_A4E,1603
fastavro/json_write.py,sha256=2gvOCrlhAK7oFiEq3XqoZLYjykvAHC_2ZAM43WzMWnY,2908
fastavro/logical_readers.py,sha256=UmV4ln0Cx0EYVXMX4ythXIzEQ5cNqooWomnJHHNvfkI,217
fastavro/logical_writers.py,sha256=yqOFgc_qR6f_rF01jsEQsWknOJhycHYM4bgtimoeBaY,217
fastavro/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fastavro/read.py,sha256=D-PkgEuIl-qsZ_cbfOB4VEe_i3WinxqbqSos4UjMFtw,775
fastavro/repository/__init__.py,sha256=ZJwfQDOD_JSjNMl4GIlH0j3n25DT6IwjW16UHf7wU1I,210
fastavro/repository/__pycache__/__init__.cpython-313.pyc,,
fastavro/repository/__pycache__/base.cpython-313.pyc,,
fastavro/repository/__pycache__/flat_dict.cpython-313.pyc,,
fastavro/repository/base.py,sha256=Ax6ypXRKqnraq6_ETbAB4ogJQbNFtKUtxcnwGO0HYFA,185
fastavro/repository/flat_dict.py,sha256=4F1SPt6WtPCVJOLWJilbJg-OcY2GS-x-NwXl_NNh0hI,764
fastavro/schema.py,sha256=EL9KW1UUr_mZe23Q2qJodOjTpeQ8u7ru6zOmHEe9MCY,1108
fastavro/types.py,sha256=nrKQpXqPZ5Bu_rO2ZqZlm56adPVBk7i9OrZF1QOnl7M,464
fastavro/utils.py,sha256=ZG07EnfFsIWLafwG5VEfAoUZex8hyk586gt6eP9Ltrc,8716
fastavro/validation.py,sha256=4MKAk-bp59xqnYN_v_O1uD99xYhgUVaHjWVzRNNNp0I,422
fastavro/write.py,sha256=NGtikG8VeUJ92d-CPJpIijQgJDwFvgpOMD1XD35b3xE,457
