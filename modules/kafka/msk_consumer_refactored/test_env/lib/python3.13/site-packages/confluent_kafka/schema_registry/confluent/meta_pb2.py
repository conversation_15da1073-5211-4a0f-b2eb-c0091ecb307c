# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: confluent/meta.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import descriptor_pb2 as google_dot_protobuf_dot_descriptor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14\x63onfluent/meta.proto\x12\tconfluent\x1a google/protobuf/descriptor.proto\"}\n\x04Meta\x12\x0b\n\x03\x64oc\x18\x01 \x01(\t\x12+\n\x06params\x18\x02 \x03(\x0b\x32\x1b.confluent.Meta.ParamsEntry\x12\x0c\n\x04tags\x18\x03 \x03(\t\x1a-\n\x0bParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01:A\n\tfile_meta\x12\x1c.google.protobuf.FileOptions\x18\xc0\x08 \x01(\x0b\x32\x0f.confluent.Meta:G\n\x0cmessage_meta\x12\x1f.google.protobuf.MessageOptions\x18\xc0\x08 \x01(\x0b\x32\x0f.confluent.Meta:C\n\nfield_meta\x12\x1d.google.protobuf.FieldOptions\x18\xc0\x08 \x01(\x0b\x32\x0f.confluent.Meta:A\n\tenum_meta\x12\x1c.google.protobuf.EnumOptions\x18\xc0\x08 \x01(\x0b\x32\x0f.confluent.Meta:L\n\x0f\x65num_value_meta\x12!.google.protobuf.EnumValueOptions\x18\xc0\x08 \x01(\x0b\x32\x0f.confluent.MetaB\x0eZ\x0c../confluentb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'confluent.meta_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:
  google_dot_protobuf_dot_descriptor__pb2.FileOptions.RegisterExtension(file_meta)
  google_dot_protobuf_dot_descriptor__pb2.MessageOptions.RegisterExtension(message_meta)
  google_dot_protobuf_dot_descriptor__pb2.FieldOptions.RegisterExtension(field_meta)
  google_dot_protobuf_dot_descriptor__pb2.EnumOptions.RegisterExtension(enum_meta)
  google_dot_protobuf_dot_descriptor__pb2.EnumValueOptions.RegisterExtension(enum_value_meta)

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\014../confluent'
  _META_PARAMSENTRY._options = None
  _META_PARAMSENTRY._serialized_options = b'8\001'
  _META._serialized_start=69
  _META._serialized_end=194
  _META_PARAMSENTRY._serialized_start=149
  _META_PARAMSENTRY._serialized_end=194
# @@protoc_insertion_point(module_scope)
