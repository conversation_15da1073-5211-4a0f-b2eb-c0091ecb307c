#!/usr/bin/env python
#
# Copyright 2016 Confluent Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#


class SerializerError(Exception):
    """Generic error from serializer package"""

    def __init__(self, message):
        self.message = message

    def __repr__(self):
        return '{klass}(error={error})'.format(
            klass=self.__class__.__name__,
            error=self.message
        )

    def __str__(self):
        return self.message


class KeySerializerError(SerializerError):
    pass


class ValueSerializerError(SerializerError):
    pass
