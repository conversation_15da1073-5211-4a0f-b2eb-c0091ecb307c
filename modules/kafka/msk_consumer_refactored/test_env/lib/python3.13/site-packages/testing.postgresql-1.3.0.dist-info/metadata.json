{"generator": "bdist_wheel (0.26.0)", "summary": "automatically setups a postgresql instance in a temporary directory, and destroys it after testing", "classifiers": ["Development Status :: 4 - Beta", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Apache Software License", "Programming Language :: Python", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3.2", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Topic :: Database", "Topic :: Software Development", "Topic :: Software Development :: Testing"], "extensions": {"python.details": {"project_urls": {"Home": "https://github.com/tk0miya/testing.postgresql"}, "contacts": [{"email": "i.tko<PERSON> at gmail.com", "name": "<PERSON><PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}}}, "license": "Apache License 2.0", "metadata_version": "2.0", "name": "testing.postgresql", "extras": ["testing"], "run_requires": [{"requires": ["SQLAlchemy", "nose", "psycopg2"], "extra": "testing"}, {"requires": ["pg8000 (>=1.10)", "testing.common.database"]}], "version": "1.3.0"}