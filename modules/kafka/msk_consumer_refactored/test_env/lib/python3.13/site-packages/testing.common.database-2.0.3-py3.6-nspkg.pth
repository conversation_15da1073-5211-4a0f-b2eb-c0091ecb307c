import sys, types, os;has_mfs = sys.version_info > (3, 5);p = os.path.join(sys._getframe(1).f_locals['sitedir'], *('testing',));importlib = has_mfs and __import__('importlib.util');has_mfs and __import__('importlib.machinery');m = has_mfs and sys.modules.setdefault('testing', importlib.util.module_from_spec(importlib.machinery.PathFinder.find_spec('testing', [os.path.dirname(p)])));m = m or sys.modules.setdefault('testing', types.ModuleType('testing'));mp = (m or []) and m.__dict__.setdefault('__path__',[]);(p not in mp) and mp.append(p)
import sys, types, os;has_mfs = sys.version_info > (3, 5);p = os.path.join(sys._getframe(1).f_locals['sitedir'], *('testing',));importlib = has_mfs and __import__('importlib.util');has_mfs and __import__('importlib.machinery');m = has_mfs and sys.modules.setdefault('testing', importlib.util.module_from_spec(importlib.machinery.PathFinder.find_spec('testing', [os.path.dirname(p)])));m = m or sys.modules.setdefault('testing', types.ModuleType('testing'));mp = (m or []) and m.__dict__.setdefault('__path__',[]);(p not in mp) and mp.append(p)
import sys, types, os;has_mfs = sys.version_info > (3, 5);p = os.path.join(sys._getframe(1).f_locals['sitedir'], *('testing', 'common'));importlib = has_mfs and __import__('importlib.util');has_mfs and __import__('importlib.machinery');m = has_mfs and sys.modules.setdefault('testing.common', importlib.util.module_from_spec(importlib.machinery.PathFinder.find_spec('testing.common', [os.path.dirname(p)])));m = m or sys.modules.setdefault('testing.common', types.ModuleType('testing.common'));mp = (m or []) and m.__dict__.setdefault('__path__',[]);(p not in mp) and mp.append(p);m and setattr(sys.modules['testing'], 'common', m)
