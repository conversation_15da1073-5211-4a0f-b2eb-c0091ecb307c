{"pagination": {"DescribeLoadBalancers": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "PageSize", "result_key": "LoadBalancers"}, "DescribeTargetGroups": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "PageSize", "result_key": "TargetGroups"}, "DescribeListeners": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "limit_key": "PageSize", "result_key": "Listeners"}, "DescribeAccountLimits": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "Limits"}, "DescribeListenerCertificates": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "Certificates"}, "DescribeRules": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "Rules"}, "DescribeSSLPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "SslPolicies"}, "DescribeTrustStoreAssociations": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "TrustStoreAssociations"}, "DescribeTrustStoreRevocations": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "TrustStoreRevocations"}, "DescribeTrustStores": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker", "result_key": "TrustStores"}}}