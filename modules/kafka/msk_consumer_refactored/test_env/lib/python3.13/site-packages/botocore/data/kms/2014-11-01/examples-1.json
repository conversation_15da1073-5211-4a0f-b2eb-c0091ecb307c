{"version": "1.0", "examples": {"CancelKeyDeletion": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose deletion you are canceling. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}, "output": {"KeyId": "The ARN of the KMS key whose deletion you canceled."}}, "description": "The following example cancels deletion of the specified KMS key.", "id": "to-cancel-deletion-of-a-cmk-1477428535102", "title": "To cancel deletion of a KMS key"}], "ConnectCustomKeyStore": [{"input": {"CustomKeyStoreId": "cks-1234567890abcdef0"}, "output": {}, "comments": {"input": {"CustomKeyStoreId": "The ID of the AWS KMS custom key store."}, "output": {}}, "description": "This example connects an AWS KMS custom key store to its AWS CloudHSM cluster. This operation does not return any data. To verify that the custom key store is connected, use the <code>DescribeCustomKeyStores</code> operation.", "id": "to-connect-a-custom-key-store-to-its-cloudhsm-cluster-1628626947750", "title": "To connect a custom key store to its CloudHSM cluster"}], "CreateAlias": [{"input": {"AliasName": "alias/<PERSON><PERSON><PERSON><PERSON><PERSON>", "TargetKeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"AliasName": "The alias to create. Aliases must begin with 'alias/'. Do not use aliases that begin with 'alias/aws' because they are reserved for use by AWS.", "TargetKeyId": "The identifier of the KMS key whose alias you are creating. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example creates an alias for the specified KMS key.", "id": "to-create-an-alias-*************", "title": "To create an alias"}], "CreateCustomKeyStore": [{"input": {"CloudHsmClusterId": "cluster-1a23b4cdefg", "CustomKeyStoreName": "ExampleKeyStore", "KeyStorePassword": "kmsPswd", "TrustAnchorCertificate": "<certificate-goes-here>"}, "output": {"CustomKeyStoreId": "cks-1234567890abcdef0"}, "comments": {"input": {"CloudHsmClusterId": "The ID of the CloudHSM cluster.", "CustomKeyStoreName": "A friendly name for the custom key store.", "KeyStorePassword": "The password for the kmsuser CU account in the specified cluster.", "TrustAnchorCertificate": "The content of the customerCA.crt file that you created when you initialized the cluster."}, "output": {"CustomKeyStoreId": "The ID of the new custom key store."}}, "description": "This example creates a custom key store that is associated with an AWS CloudHSM cluster.", "id": "to-create-an-aws-cloudhsm-custom-key-store-*************", "title": "To create an AWS CloudHSM custom key store"}], "CreateGrant": [{"input": {"GranteePrincipal": "arn:aws:iam::************:role/ExampleRole", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Operations": ["Encrypt", "Decrypt"]}, "output": {"GrantId": "0c237476b39f8bc44e45212e08498fbe3151305030726c0590dd8d3e9f3d6a60", "GrantToken": "AQpAM2RhZTk1MGMyNTk2ZmZmMzEyYWVhOWViN2I1MWM4Mzc0MWFiYjc0ZDE1ODkyNGFlNTIzODZhMzgyZjBlNGY3NiKIAgEBAgB4Pa6VDCWW__MSrqnre1HIN0Grt00ViSSuUjhqOC8OT3YAAADfMIHcBgkqhkiG9w0BBwaggc4wgcsCAQAwgcUGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMmqLyBTAegIn9XlK5AgEQgIGXZQjkBcl1dykDdqZBUQ6L1OfUivQy7JVYO2-ZJP7m6f1g8GzV47HX5phdtONAP7K_HQIflcgpkoCqd_fUnE114mSmiagWkbQ5sqAVV3ov-VeqgrvMe5ZFEWLMSluvBAqdjHEdMIkHMlhlj4ENZbzBfo9Wxk8b8SnwP4kc4gGivedzFXo-dwN8fxjjq_ZZ9JFOj2ijIbj5FyogDCN0drOfi8RORSEuCEmPvjFRMFAwcmwFkN2NPp89amA"}, "comments": {"input": {"GranteePrincipal": "The identity that is given permission to perform the operations specified in the grant.", "KeyId": "The identifier of the KMS key to which the grant applies. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key.", "Operations": "A list of operations that the grant allows."}, "output": {"GrantId": "The unique identifier of the grant.", "GrantToken": "The grant token."}}, "description": "The following example creates a grant that allows the specified IAM role to encrypt data with the specified KMS key.", "id": "to-create-a-grant-*************", "title": "To create a grant"}], "CreateKey": [{"input": {}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": "2017-07-05T14:04:55-07:00", "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "SYMMETRIC_DEFAULT", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": false, "Origin": "AWS_KMS"}}, "comments": {"input": {"Tags": "One or more tags. Each tag consists of a tag key and a tag value."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "The following example creates a symmetric KMS key for encryption and decryption. No parameters are required for this operation.", "id": "to-create-a-cmk-*************", "title": "To create a KMS key"}, {"input": {"KeySpec": "RSA_4096", "KeyUsage": "ENCRYPT_DECRYPT"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": "2021-04-05T14:04:55-07:00", "CustomerMasterKeySpec": "RSA_4096", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["RSAES_OAEP_SHA_1", "RSAES_OAEP_SHA_256"], "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "RSA_4096", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": false, "Origin": "AWS_KMS"}}, "comments": {"input": {"KeySpec": "Describes the type of key material in the KMS key.", "KeyUsage": "The cryptographic operations for which you can use the KMS key."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "This example creates a KMS key that contains an asymmetric RSA key pair for encryption and decryption. The key spec and key usage can't be changed after the key is created.", "id": "to-create-an-asymmetric-rsa-kms-key-for-encryption-and-decryption-*************", "title": "To create an asymmetric RSA KMS key for encryption and decryption"}, {"input": {"KeySpec": "ECC_NIST_P521", "KeyUsage": "SIGN_VERIFY"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": "2019-12-02T07:48:55-07:00", "CustomerMasterKeySpec": "ECC_NIST_P521", "Description": "", "Enabled": true, "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "ECC_NIST_P521", "KeyState": "Enabled", "KeyUsage": "SIGN_VERIFY", "MultiRegion": false, "Origin": "AWS_KMS", "SigningAlgorithms": ["ECDSA_SHA_512"]}}, "comments": {"input": {"KeySpec": "Describes the type of key material in the KMS key.", "KeyUsage": "The cryptographic operations for which you can use the KMS key."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "This example creates a KMS key that contains an asymmetric elliptic curve (ECC) key pair for signing and verification. The key usage is required even though \"SIGN_VERIFY\" is the only valid value for ECC KMS keys. The key spec and key usage can't be changed after the key is created.", "id": "to-create-an-asymmetric-elliptic-curve-kms-key-for-signing-and-verification-*************", "title": "To create an asymmetric elliptic curve KMS key for signing and verification"}, {"input": {"MultiRegion": true}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-west-2:************:key/mrk-1234abcd12ab34cd56ef12345678990ab", "CreationDate": "2021-09-02T016:15:21-09:00", "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "mrk-1234abcd12ab34cd56ef12345678990ab", "KeyManager": "CUSTOMER", "KeySpec": "SYMMETRIC_DEFAULT", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": true, "MultiRegionConfiguration": {"MultiRegionKeyType": "PRIMARY", "PrimaryKey": {"Arn": "arn:aws:kms:us-west-2:************:key/mrk-1234abcd12ab34cd56ef12345678990ab", "Region": "us-west-2"}, "ReplicaKeys": []}, "Origin": "AWS_KMS"}}, "comments": {"input": {"MultiRegion": "Indicates whether the KMS key is a multi-Region (True) or regional (False) key."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "This example creates a multi-Region primary symmetric encryption key. Because the default values for all parameters create a symmetric encryption key, only the MultiRegion parameter is required for this KMS key.", "id": "to-create-a-multi-region-primary-kms-key-*************", "title": "To create a multi-Region primary KMS key"}, {"input": {"Origin": "EXTERNAL"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": "2019-12-02T07:48:55-07:00", "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": false, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "SYMMETRIC_DEFAULT", "KeyState": "PendingImport", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": false, "Origin": "EXTERNAL"}}, "comments": {"input": {"Origin": "The source of the key material for the KMS key."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "This example creates a KMS key with no key material. When the operation is complete, you can import your own key material into the KMS key. To create this KMS key, set the Origin parameter to EXTERNAL. ", "id": "to-create-a-kms-key-for-imported-key-material-*************", "title": "To create a KMS key for imported key material"}, {"input": {"CustomKeyStoreId": "cks-1234567890abcdef0", "Origin": "AWS_CLOUDHSM"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CloudHsmClusterId": "cluster-1a23b4cdefg", "CreationDate": "2019-12-02T07:48:55-07:00", "CustomKeyStoreId": "cks-1234567890abcdef0", "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "SYMMETRIC_DEFAULT", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": false, "Origin": "AWS_CLOUDHSM"}}, "comments": {"input": {"CustomKeyStoreId": "Identifies the custom key store that hosts the KMS key.", "Origin": "Indicates the source of the key material for the KMS key."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "This example creates a KMS key in the specified custom key store. The operation creates the KMS key and its metadata in AWS KMS and the key material in the AWS CloudHSM cluster associated with the custom key store. This example requires the Origin and CustomKeyStoreId parameters.", "id": "to-create-a-kms-key-in-a-custom-key-store-*************", "title": "To create a KMS key in a custom key store"}, {"input": {"KeySpec": "HMAC_384", "KeyUsage": "GENERATE_VERIFY_MAC"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": "2022-04-05T14:04:55-07:00", "CustomerMasterKeySpec": "HMAC_384", "Description": "", "Enabled": true, "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "HMAC_384", "KeyState": "Enabled", "KeyUsage": "GENERATE_VERIFY_MAC", "MacAlgorithms": ["HMAC_SHA_384"], "MultiRegion": false, "Origin": "AWS_KMS"}}, "comments": {"input": {"KeySpec": "Describes the type of key material in the KMS key.", "KeyUsage": "The cryptographic operations for which you can use the KMS key."}, "output": {"KeyMetadata": "Detailed information about the KMS key that this operation creates."}}, "description": "This example creates a 384-bit symmetric HMAC KMS key. The GENERATE_VERIFY_MAC key usage value is required even though it's the only valid value for HMAC KMS keys. The key spec and key usage can't be changed after the key is created. ", "id": "to-create-an-hmac-kms-key-1630628752841", "title": "To create an HMAC KMS key"}], "Decrypt": [{"input": {"CiphertextBlob": "<binary data>", "KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Plaintext": "<binary data>"}, "comments": {"input": {"CiphertextBlob": "The encrypted data (ciphertext).", "KeyId": "A key identifier for the KMS key to use to decrypt the data."}, "output": {"KeyId": "The Amazon Resource Name (ARN) of the KMS key that was used to decrypt the data.", "Plaintext": "The decrypted (plaintext) data."}}, "description": "The following example decrypts data that was encrypted with a KMS key.", "id": "to-decrypt-data-1478281622886", "title": "To decrypt data"}], "DeleteAlias": [{"input": {"AliasName": "alias/<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "comments": {"input": {"AliasName": "The alias to delete."}}, "description": "The following example deletes the specified alias.", "id": "to-delete-an-alias-1478285209338", "title": "To delete an alias"}], "DeleteCustomKeyStore": [{"input": {"CustomKeyStoreId": "cks-1234567890abcdef0"}, "output": {}, "comments": {"input": {"CustomKeyStoreId": "The ID of the custom key store to be deleted."}, "output": {}}, "description": "This example deletes a custom key store from AWS KMS. This operation does not delete the AWS CloudHSM cluster that was associated with the CloudHSM cluster. This operation doesn't return any data. To verify that the operation was successful, use the DescribeCustomKeyStores operation.  ", "id": "to-delete-a-custom-key-store-from-aws-kms-1628630837145", "title": "To delete a custom key store from AWS KMS"}], "DeleteImportedKeyMaterial": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose imported key material you are deleting. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example deletes the imported key material from the specified KMS key.", "id": "to-delete-imported-key-material-*************", "title": "To delete imported key material"}], "DescribeCustomKeyStores": [{"input": {}, "output": {"CustomKeyStores": []}, "comments": {"input": {}, "output": {"CustomKeyStores": "Details about each custom key store in the account and Region."}}, "description": "This example gets detailed information about all AWS KMS custom key stores in an AWS account and Region. To get all key stores, do not enter a custom key store name or ID.", "id": "to-get-detailed-information-about-custom-key-stores-in-the-account-and-region-*************", "title": "To get detailed information about custom key stores in the account and Region"}, {"input": {"CustomKeyStoreName": "ExampleKeyStore"}, "output": {"CustomKeyStores": [{"CloudHsmClusterId": "cluster-1a23b4cdefg", "ConnectionState": "CONNECTED", "CreationDate": "1.499288695918E9", "CustomKeyStoreId": "cks-1234567890abcdef0", "CustomKeyStoreName": "ExampleKeyStore", "TrustAnchorCertificate": "<certificate appears here>"}]}, "comments": {"input": {"CustomKeyStoreName": "The friendly name of the custom key store."}, "output": {"CustomKeyStores": "Detailed information about the specified custom key store."}}, "description": "This example gets detailed information about a particular AWS KMS custom key store that is associate with an AWS CloudHSM cluster. To limit the output to a particular custom key store, provide the custom key store name or ID. ", "id": "to-get-detailed-information-about-a-custom-key-store-associated-with-a-cloudhsm-cluster-*************", "title": "To get detailed information about a custom key store associated with a CloudHSM cluster."}], "DescribeKey": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": "2017-07-05T14:04:55-07:00", "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "SYMMETRIC_DEFAULT", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": false, "Origin": "AWS_KMS"}}, "comments": {"input": {"KeyId": "An identifier for the KMS key. You can use the key ID, key AR<PERSON>, alias name, alias <PERSON><PERSON> of the KMS key."}, "output": {"KeyMetadata": "An object that contains information about the specified KMS key."}}, "description": "The following example gets metadata for a symmetric encryption KMS key.", "id": "get-key-details-*************", "title": "To get details about a KMS key"}, {"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": **********.317, "CustomerMasterKeySpec": "RSA_2048", "Description": "", "Enabled": false, "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeySpec": "RSA_2048", "KeyState": "Disabled", "KeyUsage": "SIGN_VERIFY", "MultiRegion": false, "Origin": "AWS_KMS", "SigningAlgorithms": ["RSASSA_PKCS1_V1_5_SHA_256", "RSASSA_PKCS1_V1_5_SHA_384", "RSASSA_PKCS1_V1_5_SHA_512", "RSASSA_PSS_SHA_256", "RSASSA_PSS_SHA_384", "RSASSA_PSS_SHA_512"]}}, "comments": {"input": {"KeyId": "An identifier for the KMS key. You can use the key ID, key AR<PERSON>, alias name, alias <PERSON><PERSON> of the KMS key."}, "output": {"KeyMetadata": "An object that contains information about the specified KMS key."}}, "description": "The following example gets metadata for an asymmetric RSA KMS key used for signing and verification.", "id": "to-get-details-about-an-rsa-asymmetric-kms-key-*************", "title": "To get details about an RSA asymmetric KMS key"}, {"input": {"KeyId": "arn:aws:kms:ap-northeast-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:ap-northeast-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "CreationDate": **********.918, "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "mrk-1234abcd12ab34cd56ef1234567890ab", "KeyManager": "CUSTOMER", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": true, "MultiRegionConfiguration": {"MultiRegionKeyType": "PRIMARY", "PrimaryKey": {"Arn": "arn:aws:kms:us-west-2:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "Region": "us-west-2"}, "ReplicaKeys": [{"Arn": "arn:aws:kms:eu-west-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "Region": "eu-west-1"}, {"Arn": "arn:aws:kms:ap-northeast-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "Region": "ap-northeast-1"}, {"Arn": "arn:aws:kms:sa-east-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "Region": "sa-east-1"}]}, "Origin": "AWS_KMS"}}, "comments": {"input": {"KeyId": "An identifier for the KMS key. You can use the key ID, key AR<PERSON>, alias name, alias <PERSON><PERSON> of the KMS key."}, "output": {"KeyMetadata": "An object that contains information about the specified KMS key."}}, "description": "The following example gets metadata for a multi-Region replica key. This multi-Region key is a symmetric encryption key. DescribeKey returns information about the primary key and all of its replicas.", "id": "to-get-details-about-a-multi-region-key-*************", "title": "To get details about a multi-Region key"}, {"input": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"KeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "CreationDate": **********.664, "CustomerMasterKeySpec": "HMAC_256", "Description": "Development test key", "Enabled": true, "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "KeyManager": "CUSTOMER", "KeyState": "Enabled", "KeyUsage": "GENERATE_VERIFY_MAC", "MacAlgorithms": ["HMAC_SHA_256"], "MultiRegion": false, "Origin": "AWS_KMS"}}, "comments": {"input": {"KeyId": "An identifier for the KMS key. You can use the key ID, key AR<PERSON>, alias name, alias <PERSON><PERSON> of the KMS key."}, "output": {"KeyMetadata": "An object that contains information about the specified KMS key."}}, "description": "The following example gets the metadata of an HMAC KMS key. ", "id": "to-get-details-about-an-hmac-kms-key-*************", "title": "To get details about an HMAC KMS key"}], "DisableKey": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to disable. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example disables the specified KMS key.", "id": "to-disable-a-cmk-1478566583659", "title": "To disable a KMS key"}], "DisableKeyRotation": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose key material will no longer be rotated. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example disables automatic annual rotation of the key material for the specified KMS key.", "id": "to-disable-automatic-rotation-of-key-material-1478624396092", "title": "To disable automatic rotation of key material"}], "DisconnectCustomKeyStore": [{"input": {"CustomKeyStoreId": "cks-1234567890abcdef0"}, "output": {}, "comments": {"input": {"CustomKeyStoreId": "The ID of the custom key store."}, "output": {}}, "description": "This example disconnects an AWS KMS custom key store from its AWS CloudHSM cluster. This operation doesn't return any data. To verify that the custom key store is disconnected, use the <code>DescribeCustomKeyStores</code> operation.", "id": "to-disconnect-a-custom-key-store-from-its-cloudhsm-cluster-1628627955156", "title": "To disconnect a custom key store from its CloudHSM cluster"}], "EnableKey": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to enable. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example enables the specified KMS key.", "id": "to-enable-a-cmk-1478627501129", "title": "To enable a KMS key"}], "EnableKeyRotation": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose key material will be rotated annually. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example enables automatic annual rotation of the key material for the specified KMS key.", "id": "to-enable-automatic-rotation-of-key-material-1478629109677", "title": "To enable automatic rotation of key material"}], "Encrypt": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "Plaintext": "<binary data>"}, "output": {"CiphertextBlob": "<binary data>", "KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to use for encryption. You can use the key ID or Amazon Resource Name (ARN) of the KMS key, or the name or ARN of an alias that refers to the KMS key.", "Plaintext": "The data to encrypt."}, "output": {"CiphertextBlob": "The encrypted data (ciphertext).", "KeyId": "The ARN of the KMS key that was used to encrypt the data."}}, "description": "The following example encrypts data with the specified KMS key.", "id": "to-encrypt-data-1478906026012", "title": "To encrypt data"}], "GenerateDataKey": [{"input": {"KeyId": "alias/<PERSON><PERSON><PERSON><PERSON><PERSON>", "KeySpec": "AES_256"}, "output": {"CiphertextBlob": "<binary data>", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Plaintext": "<binary data>"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to use to encrypt the data key. You can use the key ID or Amazon Resource Name (ARN) of the KMS key, or the name or ARN of an alias that refers to the KMS key.", "KeySpec": "Specifies the type of data key to return."}, "output": {"CiphertextBlob": "The encrypted data key.", "KeyId": "The ARN of the KMS key that was used to encrypt the data key.", "Plaintext": "The unencrypted (plaintext) data key."}}, "description": "The following example generates a 256-bit symmetric data encryption key (data key) in two formats. One is the unencrypted (plainext) data key, and the other is the data key encrypted with the specified KMS key.", "id": "to-generate-a-data-key-1478912956062", "title": "To generate a data key"}], "GenerateDataKeyPair": [{"input": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "KeyPairSpec": "RSA_3072"}, "output": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "KeyPairSpec": "RSA_3072", "PrivateKeyCiphertextBlob": "<binary data>", "PrivateKeyPlaintext": "<binary data>", "PublicKey": "<binary data>"}, "comments": {"input": {"KeyId": "The key ID of the symmetric encryption KMS key that encrypts the private RSA key in the data key pair.", "KeyPairSpec": "The requested key spec of the RSA data key pair."}, "output": {"KeyId": "The key ARN of the symmetric encryption KMS key that was used to encrypt the private key.", "KeyPairSpec": "The actual key spec of the RSA data key pair.", "PrivateKeyCiphertextBlob": "The encrypted private key of the RSA data key pair.", "PrivateKeyPlaintext": "The plaintext private key of the RSA data key pair.", "PublicKey": "The public key (plaintext) of the RSA data key pair."}}, "description": "This example generates an RSA data key pair for encryption and decryption. The operation returns a plaintext public key and private key, and a copy of the private key that is encrypted under a symmetric encryption KMS key that you specify.", "id": "to-generate-an-rsa-key-pair-for-encryption-and-decryption-1628619376878", "title": "To generate an RSA key pair for encryption and decryption"}], "GenerateDataKeyPairWithoutPlaintext": [{"input": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "KeyPairSpec": "ECC_NIST_P521"}, "output": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "KeyPairSpec": "ECC_NIST_P521", "PrivateKeyCiphertextBlob": "<binary data>", "PublicKey": "<binary data>"}, "comments": {"input": {"KeyId": "The symmetric encryption KMS key that encrypts the private key of the ECC data key pair.", "KeyPairSpec": "The requested key spec of the ECC asymmetric data key pair."}, "output": {"KeyId": "The key ARN of the symmetric encryption KMS key that encrypted the private key in the ECC asymmetric data key pair.", "KeyPairSpec": "The actual key spec of the ECC asymmetric data key pair.", "PrivateKeyCiphertextBlob": "The encrypted private key of the asymmetric ECC data key pair.", "PublicKey": "The public key (plaintext)."}}, "description": "This example returns an asymmetric elliptic curve (ECC) data key pair. The private key is encrypted under the symmetric encryption KMS key that you specify. This operation doesn't return a plaintext (unencrypted) private key.", "id": "to-generate-an-asymmetric-data-key-pair-without-a-plaintext-key-1628620971564", "title": "To generate an asymmetric data key pair without a plaintext key"}], "GenerateDataKeyWithoutPlaintext": [{"input": {"KeyId": "alias/<PERSON><PERSON><PERSON><PERSON><PERSON>", "KeySpec": "AES_256"}, "output": {"CiphertextBlob": "<binary data>", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to use to encrypt the data key. You can use the key ID or Amazon Resource Name (ARN) of the KMS key, or the name or ARN of an alias that refers to the KMS key.", "KeySpec": "Specifies the type of data key to return."}, "output": {"CiphertextBlob": "The encrypted data key.", "KeyId": "The ARN of the KMS key that was used to encrypt the data key."}}, "description": "The following example generates an encrypted copy of a 256-bit symmetric data encryption key (data key). The data key is encrypted with the specified KMS key.", "id": "to-generate-an-encrypted-data-key-1478914121134", "title": "To generate an encrypted data key"}], "GenerateMac": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "MacAlgorithm": "HMAC_SHA_384", "Message": "Hello World"}, "output": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Mac": "<HMAC_TAG>", "MacAlgorithm": "HMAC_SHA_384"}, "comments": {"input": {"KeyId": "The HMAC KMS key input to the HMAC algorithm.", "MacAlgorithm": "The HMAC algorithm requested for the operation.", "Message": "The message input to the HMAC algorithm."}, "output": {"KeyId": "The key ARN of the HMAC KMS key used in the operation.", "Mac": "The HMAC tag that results from this operation.", "MacAlgorithm": "The HMAC algorithm used in the operation."}}, "description": "This example generates an HMAC for a message, an HMAC KMS key, and a MAC algorithm. The algorithm must be supported by the specified HMAC KMS key.", "id": "to-generate-an-hmac-for-a-message-1631570135665", "title": "To generate an HMAC for a message"}], "GenerateRandom": [{"input": {"NumberOfBytes": 32}, "output": {"Plaintext": "<binary data>"}, "comments": {"input": {"NumberOfBytes": "The length of the random data, specified in number of bytes."}, "output": {"Plaintext": "The random data."}}, "description": "The following example generates 32 bytes of random data.", "id": "to-generate-random-data-1479163645600", "title": "To generate random data"}], "GetKeyPolicy": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "PolicyName": "default"}, "output": {"Policy": "{\n  \"Version\" : \"2012-10-17\",\n  \"Id\" : \"key-default-1\",\n  \"Statement\" : [ {\n    \"Sid\" : \"Enable IAM User Permissions\",\n    \"Effect\" : \"Allow\",\n    \"Principal\" : {\n      \"AWS\" : \"arn:aws:iam::************:root\"\n    },\n    \"Action\" : \"kms:*\",\n    \"Resource\" : \"*\"\n  } ]\n}"}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose key policy you want to retrieve. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key.", "PolicyName": "The name of the key policy to retrieve."}, "output": {"Policy": "The key policy document."}}, "description": "The following example retrieves the key policy for the specified KMS key.", "id": "to-retrieve-a-key-policy-1479170128325", "title": "To retrieve a key policy"}], "GetKeyRotationStatus": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"KeyRotationEnabled": true}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose key material rotation status you want to retrieve. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}, "output": {"KeyRotationEnabled": "A boolean that indicates the key material rotation status. Returns true when automatic annual rotation of the key material is enabled, or false when it is not."}}, "description": "The following example retrieves the status of automatic annual rotation of the key material for the specified KMS key.", "id": "to-retrieve-the-rotation-status-for-a-cmk-1479172287408", "title": "To retrieve the rotation status for a KMS key"}], "GetParametersForImport": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "WrappingAlgorithm": "RSAES_OAEP_SHA_1", "WrappingKeySpec": "RSA_2048"}, "output": {"ImportToken": "<binary data>", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "ParametersValidTo": "2016-12-01T14:52:17-08:00", "PublicKey": "<binary data>"}, "comments": {"input": {"KeyId": "The identifier of the KMS key for which to retrieve the public key and import token. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key.", "WrappingAlgorithm": "The algorithm that you will use to encrypt the key material before importing it.", "WrappingKeySpec": "The type of wrapping key (public key) to return in the response."}, "output": {"ImportToken": "The import token to send with a subsequent ImportKeyMaterial request.", "KeyId": "The ARN of the KMS key for which you are retrieving the public key and import token. This is the same KMS key specified in the request.", "ParametersValidTo": "The time at which the import token and public key are no longer valid.", "PublicKey": "The public key to use to encrypt the key material before importing it."}}, "description": "The following example retrieves the public key and import token for the specified KMS key.", "id": "to-retrieve-the-public-key-and-import-token-for-a-cmk-1480626483211", "title": "To retrieve the public key and import token for a KMS key"}], "GetPublicKey": [{"input": {"KeyId": "arn:aws:kms:us-west-2:************:key/0987dcba-09fe-87dc-65ba-ab0987654321"}, "output": {"CustomerMasterKeySpec": "RSA_4096", "EncryptionAlgorithms": ["RSAES_OAEP_SHA_1", "RSAES_OAEP_SHA_256"], "KeyId": "arn:aws:kms:us-west-2:************:key/0987dcba-09fe-87dc-65ba-ab0987654321", "KeyUsage": "ENCRYPT_DECRYPT", "PublicKey": "<binary data>"}, "comments": {"input": {"KeyId": "The key ARN of the asymmetric KMS key."}, "output": {"CustomerMasterKeySpec": "The key spec of the asymmetric KMS key from which the public key was downloaded.", "EncryptionAlgorithms": "The encryption algorithms supported by the asymmetric KMS key that was downloaded.", "KeyId": "The key ARN of the asymmetric KMS key from which the public key was downloaded.", "KeyUsage": "The key usage of the asymmetric KMS key from which the public key was downloaded.", "PublicKey": "The public key (plaintext) of the asymmetric KMS key."}}, "description": "This example gets the public key of an asymmetric RSA KMS key used for encryption and decryption. The operation returns the key spec, key usage, and encryption or signing algorithms to help you use the public key correctly outside of AWS KMS.", "id": "to-download-the-public-key-of-an-asymmetric-kms-key-1628621691873", "title": "To download the public key of an asymmetric KMS key"}], "ImportKeyMaterial": [{"input": {"EncryptedKeyMaterial": "<binary data>", "ExpirationModel": "KEY_MATERIAL_DOES_NOT_EXPIRE", "ImportToken": "<binary data>", "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"EncryptedKeyMaterial": "The encrypted key material to import.", "ExpirationModel": "A value that specifies whether the key material expires.", "ImportToken": "The import token that you received in the response to a previous GetParametersForImport request.", "KeyId": "The identifier of the KMS key to import the key material into. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example imports key material into the specified KMS key.", "id": "to-import-key-material-into-a-cmk-1480630551969", "title": "To import key material into a KMS key"}], "ListAliases": [{"output": {"Aliases": [{"AliasArn": "arn:aws:kms:us-east-2:************:alias/aws/acm", "AliasName": "alias/aws/acm", "TargetKeyId": "da03f6f7-d279-427a-9cae-de48d07e5b66"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/aws/ebs", "AliasName": "alias/aws/ebs", "TargetKeyId": "25a217e7-7170-4b8c-8bf6-045ea5f70e5b"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/aws/rds", "AliasName": "alias/aws/rds", "TargetKeyId": "7ec3104e-c3f2-4b5c-bf42-bfc4772c6685"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/aws/redshift", "AliasName": "alias/aws/redshift", "TargetKeyId": "08f7a25a-69e2-4fb5-8f10-393db27326fa"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/aws/s3", "AliasName": "alias/aws/s3", "TargetKeyId": "d2b0f1a3-580d-4f79-b836-bc983be8cfa5"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/example1", "AliasName": "alias/example1", "TargetKeyId": "4da1e216-62d0-46c5-a7c0-5f3a3d2f8046"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/example2", "AliasName": "alias/example2", "TargetKeyId": "f32fef59-2cc2-445b-8573-2d73328acbee"}, {"AliasArn": "arn:aws:kms:us-east-2:************:alias/example3", "AliasName": "alias/example3", "TargetKeyId": "1374ef38-d34e-4d5f-b2c9-4e0daee38855"}], "Truncated": false}, "comments": {"output": {"Aliases": "A list of aliases, including the key ID of the KMS key that each alias refers to.", "Truncated": "A boolean that indicates whether there are more items in the list. Returns true when there are more items, or false when there are not."}}, "description": "The following example lists aliases.", "id": "to-list-aliases-*************", "title": "To list aliases"}], "ListGrants": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"Grants": [{"CreationDate": "2016-10-25T14:37:41-07:00", "GrantId": "91ad875e49b04a9d1f3bdeb84d821f9db6ea95e1098813f6d47f0c65fbe2a172", "GranteePrincipal": "acm.us-east-2.amazonaws.com", "IssuingAccount": "arn:aws:iam::************:root", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Operations": ["Encrypt", "ReEncryptFrom", "ReEncryptTo"], "RetiringPrincipal": "acm.us-east-2.amazonaws.com"}, {"CreationDate": "2016-10-25T14:37:41-07:00", "GrantId": "a5d67d3e207a8fc1f4928749ee3e52eb0440493a8b9cf05bbfad91655b056200", "GranteePrincipal": "acm.us-east-2.amazonaws.com", "IssuingAccount": "arn:aws:iam::************:root", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Operations": ["ReEncryptFrom", "ReEncryptTo"], "RetiringPrincipal": "acm.us-east-2.amazonaws.com"}, {"CreationDate": "2016-10-25T14:37:41-07:00", "GrantId": "c541aaf05d90cb78846a73b346fc43e65be28b7163129488c738e0c9e0628f4f", "GranteePrincipal": "acm.us-east-2.amazonaws.com", "IssuingAccount": "arn:aws:iam::************:root", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Operations": ["Encrypt", "ReEncryptFrom", "ReEncryptTo"], "RetiringPrincipal": "acm.us-east-2.amazonaws.com"}, {"CreationDate": "2016-10-25T14:37:41-07:00", "GrantId": "dd2052c67b4c76ee45caf1dc6a1e2d24e8dc744a51b36ae2f067dc540ce0105c", "GranteePrincipal": "acm.us-east-2.amazonaws.com", "IssuingAccount": "arn:aws:iam::************:root", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Operations": ["Encrypt", "ReEncryptFrom", "ReEncryptTo"], "RetiringPrincipal": "acm.us-east-2.amazonaws.com"}], "Truncated": true}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose grants you want to list. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}, "output": {"Grants": "A list of grants.", "Truncated": "A boolean that indicates whether there are more items in the list. Returns true when there are more items, or false when there are not."}}, "description": "The following example lists grants for the specified KMS key.", "id": "to-list-grants-for-a-cmk-*************", "title": "To list grants for a KMS key"}], "ListKeyPolicies": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"PolicyNames": ["default"], "Truncated": false}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose key policies you want to list. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}, "output": {"PolicyNames": "A list of key policy names.", "Truncated": "A boolean that indicates whether there are more items in the list. Returns true when there are more items, or false when there are not."}}, "description": "The following example lists key policies for the specified KMS key.", "id": "to-list-key-policies-for-a-cmk-1481069780998", "title": "To list key policies for a KMS key"}], "ListKeys": [{"output": {"Keys": [{"KeyArn": "arn:aws:kms:us-east-2:************:key/0d990263-018e-4e65-a703-eff731de951e", "KeyId": "0d990263-018e-4e65-a703-eff731de951e"}, {"KeyArn": "arn:aws:kms:us-east-2:************:key/144be297-0ae1-44ac-9c8f-93cd8c82f841", "KeyId": "144be297-0ae1-44ac-9c8f-93cd8c82f841"}, {"KeyArn": "arn:aws:kms:us-east-2:************:key/21184251-b765-428e-b852-2c7353e72571", "KeyId": "21184251-b765-428e-b852-2c7353e72571"}, {"KeyArn": "arn:aws:kms:us-east-2:************:key/214fe92f-5b03-4ae1-b350-db2a45dbe10c", "KeyId": "214fe92f-5b03-4ae1-b350-db2a45dbe10c"}, {"KeyArn": "arn:aws:kms:us-east-2:************:key/339963f2-e523-49d3-af24-a0fe752aa458", "KeyId": "339963f2-e523-49d3-af24-a0fe752aa458"}, {"KeyArn": "arn:aws:kms:us-east-2:************:key/b776a44b-df37-4438-9be4-a27494e4271a", "KeyId": "b776a44b-df37-4438-9be4-a27494e4271a"}, {"KeyArn": "arn:aws:kms:us-east-2:************:key/deaf6c9e-cf2c-46a6-bf6d-0b6d487cffbb", "KeyId": "deaf6c9e-cf2c-46a6-bf6d-0b6d487cffbb"}], "Truncated": false}, "comments": {"output": {"Keys": "A list of KMS keys, including the key ID and Amazon Resource Name (ARN) of each one.", "Truncated": "A boolean that indicates whether there are more items in the list. Returns true when there are more items, or false when there are not."}}, "description": "The following example lists KMS keys.", "id": "to-list-cmks-1481071643069", "title": "To list KMS keys"}], "ListResourceTags": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "output": {"Tags": [{"TagKey": "CostCenter", "TagValue": "87654"}, {"TagKey": "CreatedBy", "TagValue": "ExampleUser"}, {"TagKey": "Purpose", "TagValue": "Test"}], "Truncated": false}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose tags you are listing. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}, "output": {"Tags": "A list of tags.", "Truncated": "A boolean that indicates whether there are more items in the list. Returns true when there are more items, or false when there are not."}}, "description": "The following example lists tags for a KMS key.", "id": "to-list-tags-for-a-cmk-*************", "title": "To list tags for a KMS key"}], "ListRetirableGrants": [{"input": {"RetiringPrincipal": "arn:aws:iam::************:role/ExampleRole"}, "output": {"Grants": [{"CreationDate": "2016-12-07T11:09:35-08:00", "GrantId": "0c237476b39f8bc44e45212e08498fbe3151305030726c0590dd8d3e9f3d6a60", "GranteePrincipal": "arn:aws:iam::************:role/ExampleRole", "IssuingAccount": "arn:aws:iam::************:root", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Operations": ["Decrypt", "Encrypt"], "RetiringPrincipal": "arn:aws:iam::************:role/ExampleRole"}], "Truncated": false}, "comments": {"input": {"RetiringPrincipal": "The retiring principal whose grants you want to list. Use the Amazon Resource Name (ARN) of a principal such as an AWS account (root), IAM user, federated user, or assumed role user."}, "output": {"Grants": "A list of grants that the specified principal can retire.", "Truncated": "A boolean that indicates whether there are more items in the list. Returns true when there are more items, or false when there are not."}}, "description": "The following example lists the grants that the specified principal (identity) can retire.", "id": "to-list-grants-that-the-specified-principal-can-retire-*************", "title": "To list grants that the specified principal can retire"}], "PutKeyPolicy": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "Policy": "{\n    \"Version\": \"2012-10-17\",\n    \"Id\": \"custom-policy-2016-12-07\",\n    \"Statement\": [\n        {\n            \"Sid\": \"Enable IAM User Permissions\",\n            \"Effect\": \"Allow\",\n            \"Principal\": {\n                \"AWS\": \"arn:aws:iam::************:root\"\n            },\n            \"Action\": \"kms:*\",\n            \"Resource\": \"*\"\n        },\n        {\n            \"Sid\": \"Allow access for Key Administrators\",\n            \"Effect\": \"Allow\",\n            \"Principal\": {\n                \"AWS\": [\n                    \"arn:aws:iam::************:user/ExampleAdminUser\",\n                    \"arn:aws:iam::************:role/ExampleAdminRole\"\n                ]\n            },\n            \"Action\": [\n                \"kms:Create*\",\n                \"kms:Describe*\",\n                \"kms:Enable*\",\n                \"kms:List*\",\n                \"kms:Put*\",\n                \"kms:Update*\",\n                \"kms:Revoke*\",\n                \"kms:Disable*\",\n                \"kms:Get*\",\n                \"kms:Delete*\",\n                \"kms:ScheduleKeyDeletion\",\n                \"kms:CancelKeyDeletion\"\n            ],\n            \"Resource\": \"*\"\n        },\n        {\n            \"Sid\": \"Allow use of the key\",\n            \"Effect\": \"Allow\",\n            \"Principal\": {\n                \"AWS\": \"arn:aws:iam::************:role/ExamplePowerUserRole\"\n            },\n            \"Action\": [\n                \"kms:Encrypt\",\n                \"kms:Decrypt\",\n                \"kms:ReEncrypt*\",\n                \"kms:GenerateDataKey*\",\n                \"kms:DescribeKey\"\n            ],\n            \"Resource\": \"*\"\n        },\n        {\n            \"Sid\": \"Allow attachment of persistent resources\",\n            \"Effect\": \"Allow\",\n            \"Principal\": {\n                \"AWS\": \"arn:aws:iam::************:role/ExamplePowerUserRole\"\n            },\n            \"Action\": [\n                \"kms:CreateGrant\",\n                \"kms:ListGrants\",\n                \"kms:RevokeGrant\"\n            ],\n            \"Resource\": \"*\",\n            \"Condition\": {\n                \"Bool\": {\n                    \"kms:GrantIsForAWSResource\": \"true\"\n                }\n            }\n        }\n    ]\n}\n", "PolicyName": "default"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to attach the key policy to. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key.", "Policy": "The key policy document.", "PolicyName": "The name of the key policy."}}, "description": "The following example attaches a key policy to the specified KMS key.", "id": "to-attach-a-key-policy-to-a-cmk-1481147345018", "title": "To attach a key policy to a KMS key"}], "ReEncrypt": [{"input": {"CiphertextBlob": "<binary data>", "DestinationKeyId": "0987dcba-09fe-87dc-65ba-ab0987654321"}, "output": {"CiphertextBlob": "<binary data>", "KeyId": "arn:aws:kms:us-east-2:************:key/0987dcba-09fe-87dc-65ba-ab0987654321", "SourceKeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"CiphertextBlob": "The data to reencrypt.", "DestinationKeyId": "The identifier of the KMS key to use to reencrypt the data. You can use any valid key identifier.", "SourceKeyId": "The identifier of the KMS key to use to decrypt the data. You can use any valid key identifier."}, "output": {"CiphertextBlob": "The reencrypted data.", "KeyId": "The ARN of the KMS key that was used to reencrypt the data.", "SourceKeyId": "The ARN of the KMS key that was originally used to encrypt the data."}}, "description": "The following example reencrypts data with the specified KMS key.", "id": "to-reencrypt-data-*************", "title": "To reencrypt data"}], "ReplicateKey": [{"input": {"KeyId": "arn:aws:kms:us-east-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "ReplicaRegion": "us-west-2"}, "output": {"ReplicaKeyMetadata": {"AWSAccountId": "************", "Arn": "arn:aws:kms:us-west-2:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "CreationDate": **********.918, "CustomerMasterKeySpec": "SYMMETRIC_DEFAULT", "Description": "", "Enabled": true, "EncryptionAlgorithms": ["SYMMETRIC_DEFAULT"], "KeyId": "mrk-1234abcd12ab34cd56ef1234567890ab", "KeyManager": "CUSTOMER", "KeyState": "Enabled", "KeyUsage": "ENCRYPT_DECRYPT", "MultiRegion": true, "MultiRegionConfiguration": {"MultiRegionKeyType": "REPLICA", "PrimaryKey": {"Arn": "arn:aws:kms:us-east-1:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "Region": "us-east-1"}, "ReplicaKeys": [{"Arn": "arn:aws:kms:us-west-2:************:key/mrk-1234abcd12ab34cd56ef1234567890ab", "Region": "us-west-2"}]}, "Origin": "AWS_KMS"}, "ReplicaPolicy": "{\n  \"Version\" : \"2012-10-17\",\n  \"Id\" : \"key-default-1\",...}", "ReplicaTags": []}, "comments": {"input": {"KeyId": "The key ID or key ARN of the multi-Region primary key", "ReplicaRegion": "The Region of the new replica."}, "output": {"ReplicaKeyMetadata": "An object that displays detailed information about the replica key.", "ReplicaPolicy": "The key policy of the replica key. If you don't specify a key policy, the replica key gets the default key policy for a KMS key.", "ReplicaTags": "The tags on the replica key, if any."}}, "description": "This example creates a multi-Region replica key in us-west-2 of a multi-Region primary key in us-east-1. ", "id": "to-replicate-a-multi-region-key-in-a-different-aws-region-1628622402887", "title": "To replicate a multi-Region key in a different AWS Region"}], "RetireGrant": [{"input": {"GrantId": "0c237476b39f8bc44e45212e08498fbe3151305030726c0590dd8d3e9f3d6a60", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"GrantId": "The identifier of the grant to retire.", "KeyId": "The Amazon Resource Name (ARN) of the KMS key associated with the grant."}}, "description": "The following example retires a grant.", "id": "to-retire-a-grant-1481327028297", "title": "To retire a grant"}], "RevokeGrant": [{"input": {"GrantId": "0c237476b39f8bc44e45212e08498fbe3151305030726c0590dd8d3e9f3d6a60", "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"GrantId": "The identifier of the grant to revoke.", "KeyId": "The identifier of the KMS key associated with the grant. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example revokes a grant.", "id": "to-revoke-a-grant-1481329549302", "title": "To revoke a grant"}], "ScheduleKeyDeletion": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "PendingWindowInDays": 7}, "output": {"DeletionDate": "2016-12-17T16:00:00-08:00", "KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"KeyId": "The identifier of the KMS key to schedule for deletion. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key.", "PendingWindowInDays": "The waiting period, specified in number of days. After the waiting period ends, KMS deletes the KMS key."}, "output": {"DeletionDate": "The date and time after which KMS deletes the KMS key.", "KeyId": "The ARN of the KMS key that is scheduled for deletion."}}, "description": "The following example schedules the specified KMS key for deletion.", "id": "to-schedule-a-cmk-for-deletion-1481331111094", "title": "To schedule a KMS key for deletion"}], "Sign": [{"input": {"KeyId": "alias/ECC_signing_key", "Message": "<message to be signed>", "MessageType": "RAW", "SigningAlgorithm": "ECDSA_SHA_384"}, "output": {"KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "Signature": "<binary data>", "SigningAlgorithm": "ECDSA_SHA_384"}, "comments": {"input": {"KeyId": "The asymmetric KMS key to be used to generate the digital signature. This example uses an alias of the KMS key.", "Message": "Message to be signed. Use Base-64 for the CLI.", "MessageType": "Indicates whether the message is RAW or a DIGEST.", "SigningAlgorithm": "The requested signing algorithm. This must be an algorithm that the KMS key supports."}, "output": {"KeyId": "The key ARN of the asymmetric KMS key that was used to sign the message.", "Signature": "The digital signature of the message.", "SigningAlgorithm": "The actual signing algorithm that was used to generate the signature."}}, "description": "This operation uses the private key in an asymmetric elliptic curve (ECC) KMS key to generate a digital signature for a given message.", "id": "to-digitally-sign-a-message-with-an-asymmetric-kms-key-1628631433832", "title": "To digitally sign a message with an asymmetric KMS key."}], "TagResource": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "Tags": [{"TagKey": "Purpose", "TagValue": "Test"}]}, "comments": {"input": {"KeyId": "The identifier of the KMS key you are tagging. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key.", "Tags": "A list of tags."}}, "description": "The following example tags a KMS key.", "id": "to-tag-a-cmk-1483997246518", "title": "To tag a KMS key"}], "UntagResource": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "TagKeys": ["Purpose", "CostCenter"]}, "comments": {"input": {"KeyId": "The identifier of the KMS key whose tags you are removing.", "TagKeys": "A list of tag keys. Provide only the tag keys, not the tag values."}}, "description": "The following example removes tags from a KMS key.", "id": "to-remove-tags-from-a-cmk-1483997590962", "title": "To remove tags from a KMS key"}], "UpdateAlias": [{"input": {"AliasName": "alias/<PERSON><PERSON><PERSON><PERSON><PERSON>", "TargetKeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"AliasName": "The alias to update.", "TargetKeyId": "The identifier of the KMS key that the alias will refer to after this operation succeeds. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example updates the specified alias to refer to the specified KMS key.", "id": "to-update-an-alias-1481572726920", "title": "To update an alias"}], "UpdateCustomKeyStore": [{"input": {"CustomKeyStoreId": "cks-1234567890abcdef0", "KeyStorePassword": "ExamplePassword"}, "output": {}, "comments": {"input": {"CustomKeyStoreId": "The ID of the custom key store that you are updating.", "KeyStorePassword": "The password for the kmsuser crypto user in the CloudHSM cluster."}, "output": {}}, "description": "This example tells KMS the password for the kmsuser crypto user in the AWS CloudHSM cluster that is associated with the AWS KMS custom key store. (It does not change the password in the CloudHSM cluster.) This operation does not return any data.", "id": "to-edit-the-properties-of-a-custom-key-store-1628629851834", "title": "To edit the password of a custom key store"}, {"input": {"CustomKeyStoreId": "cks-1234567890abcdef0", "NewCustomKeyStoreName": "DevelopmentKeys"}, "output": {}, "comments": {"input": {"CustomKeyStoreId": "The ID of the custom key store that you are updating.", "NewCustomKeyStoreName": "A new friendly name for the custom key store."}, "output": {}}, "description": "This example changes the friendly name of the AWS KMS custom key store to the name that you specify. This operation does not return any data. To verify that the operation worked, use the DescribeCustomKeyStores operation.", "id": "to-edit-the-friendly-name-of-a-custom-key-store-1630451340904", "title": "To edit the friendly name of a custom key store"}, {"input": {"CloudHsmClusterId": "cluster-1a23b4cdefg", "CustomKeyStoreId": "cks-1234567890abcdef0"}, "output": {}, "comments": {"input": {"CloudHsmClusterId": "The ID of the AWS CloudHSM cluster that you want to associate with the custom key store. This cluster must be related to the original CloudHSM cluster for this key store.", "CustomKeyStoreId": "The ID of the custom key store that you are updating."}, "output": {}}, "description": "This example changes the cluster that is associated with a custom key store to a related cluster, such as a different backup of the same cluster. This operation does not return any data. To verify that the operation worked, use the DescribeCustomKeyStores operation.", "id": "to-associate-the-custom-key-store-with-a-different-but-related-aws-cloudhsm-cluster-1630451842438", "title": "To associate the custom key store with a different, but related, AWS CloudHSM cluster."}], "UpdateKeyDescription": [{"input": {"Description": "Example description that indicates the intended use of this KMS key.", "KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab"}, "comments": {"input": {"Description": "The updated description.", "KeyId": "The identifier of the KMS key whose description you are updating. You can use the key ID or the Amazon Resource Name (ARN) of the KMS key."}}, "description": "The following example updates the description of the specified KMS key.", "id": "to-update-the-description-of-a-cmk-1481574808619", "title": "To update the description of a KMS key"}], "Verify": [{"input": {"KeyId": "alias/ECC_signing_key", "Message": "<message to be verified>", "MessageType": "RAW", "Signature": "<binary data>", "SigningAlgorithm": "ECDSA_SHA_384"}, "output": {"KeyId": "arn:aws:kms:us-east-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "SignatureValid": true, "SigningAlgorithm": "ECDSA_SHA_384"}, "comments": {"input": {"KeyId": "The asymmetric KMS key to be used to verify the digital signature. This example uses an alias to identify the KMS key.", "Message": "The message that was signed.", "MessageType": "Indicates whether the message is RAW or a DIGEST.", "Signature": "The signature to be verified.", "SigningAlgorithm": "The signing algorithm to be used to verify the signature."}, "output": {"KeyId": "The key ARN of the asymmetric KMS key that was used to verify the digital signature.", "SignatureValid": "A value of 'true' Indicates that the signature was verified. If verification fails, the call to Verify fails.", "SigningAlgorithm": "The signing algorithm that was used to verify the signature."}}, "description": "This operation uses the public key in an elliptic curve (ECC) asymmetric key to verify a digital signature within AWS KMS. ", "id": "to-use-an-asymmetric-kms-key-to-verify-a-digital-signature-1628633365663", "title": "To use an asymmetric KMS key to verify a digital signature"}], "VerifyMac": [{"input": {"KeyId": "1234abcd-12ab-34cd-56ef-1234567890ab", "Mac": "<HMAC_TAG>", "MacAlgorithm": "HMAC_SHA_384", "Message": "Hello World"}, "output": {"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab", "MacAlgorithm": "HMAC_SHA_384", "MacValid": true}, "comments": {"input": {"KeyId": "The HMAC KMS key input to the HMAC algorithm.", "Mac": "The HMAC to be verified.", "MacAlgorithm": "The HMAC algorithm requested for the operation.", "Message": "The message input to the HMAC algorithm."}, "output": {"KeyId": "The key ARN of the HMAC key used in the operation.", "MacAlgorithm": "The HMAC algorithm used in the operation.", "MacValid": "A value of 'true' indicates that verification succeeded. If verification fails, the call to VerifyMac fails."}}, "description": "This example verifies an HMAC for a particular message, HMAC KMS keys, and MAC algorithm. A value of 'true' in the MacValid value in the response indicates that the HMAC is valid.", "id": "to-verify-an-hmac-1631570863401", "title": "To verify an HMAC"}]}}