{"pagination": {"ListImages": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "imageIds"}, "DescribeImages": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "imageDetails"}, "DescribeRepositories": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "repositories"}, "DescribeImageScanFindings": {"input_token": "nextToken", "limit_key": "maxResults", "non_aggregate_keys": ["registryId", "repositoryName", "imageId", "imageScanStatus", "imageScanFindings"], "output_token": "nextToken", "result_key": ["imageScanFindings.findings", "imageScanFindings.enhancedFindings"]}, "GetLifecyclePolicyPreview": {"input_token": "nextToken", "limit_key": "maxResults", "non_aggregate_keys": ["registryId", "repositoryName", "lifecyclePolicyText", "status", "summary"], "output_token": "nextToken", "result_key": "previewResults"}, "DescribePullThroughCacheRules": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "pullThroughCacheRules"}, "DescribeRepositoryCreationTemplates": {"input_token": "nextToken", "limit_key": "maxResults", "non_aggregate_keys": ["registryId"], "output_token": "nextToken", "result_key": "repositoryCreationTemplates"}}}