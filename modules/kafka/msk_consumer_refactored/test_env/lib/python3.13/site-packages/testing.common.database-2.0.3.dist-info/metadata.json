{"classifiers": ["Development Status :: 4 - Beta", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Apache Software License", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Topic :: Database", "Topic :: Software Development", "Topic :: Software Development :: Testing"], "description_content_type": "UNKNOWN", "extensions": {"python.details": {"contacts": [{"email": "i.tko<PERSON>@gmail.com", "name": "<PERSON><PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/tk0miya/testing.common.database"}}}, "extras": ["testing"], "generator": "bdist_wheel (0.30.0)", "license": "Apache License 2.0", "metadata_version": "2.0", "name": "testing.common.database", "run_requires": [{"extra": "testing", "requires": ["nose"]}], "summary": "utilities for testing.* packages", "version": "2.0.3"}