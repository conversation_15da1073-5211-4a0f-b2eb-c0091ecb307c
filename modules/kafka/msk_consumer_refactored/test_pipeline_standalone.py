#!/usr/bin/env python3
"""
Standalone test script for MSK consumer pipeline that works without external dependencies.
This script simulates the complete pipeline including:
1. Avro union type processing
2. Timestamp conversion with bounds checking
3. Field validation
4. PostgreSQL value adaptation
5. SQL generation
6. Database simulation

Run this to test the pipeline logic before deploying to production.
"""

import json
import sqlite3
import tempfile
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Any

def load_sample_data(filename: str = 'pyhub_sample.txt') -> Dict:
    """Load sample data from file."""
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Sample file {filename} not found")
        return {}
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing JSON: {e}")
        return {}

def process_avro_unions(record: Dict) -> Dict:
    """Process Avro union types like {"bytes": value}, {"string": value}, etc."""
    processed = {}
    
    for field_name, value in record.items():
        if isinstance(value, dict):
            if 'bytes' in value:
                try:
                    processed[field_name] = Decimal(str(value['bytes']))
                except:
                    processed[field_name] = value['bytes']
            elif 'string' in value:
                processed[field_name] = value['string']
            elif 'long' in value:
                processed[field_name] = value['long']
            elif 'int' in value:
                processed[field_name] = value['int']
            else:
                processed[field_name] = value
        else:
            processed[field_name] = value
    
    return processed

def convert_timestamps(record: Dict, timestamp_columns: List[str]) -> Dict:
    """Convert timestamp fields with robust bounds checking."""
    for field_name, value in record.items():
        if field_name in timestamp_columns and isinstance(value, int):
            try:
                if value > 1000000000000000:  # Microseconds
                    if value <= 253402300799999999:
                        converted = datetime.fromtimestamp(value / 1000000).strftime('%Y-%m-%d %H:%M:%S')
                        record[field_name] = converted
                    else:
                        print(f"  ⚠️  Timestamp too large (microseconds): {field_name} = {value}")
                elif value > 1000000000000:  # Milliseconds
                    if value <= 253402300799999:
                        converted = datetime.fromtimestamp(value / 1000).strftime('%Y-%m-%d %H:%M:%S')
                        record[field_name] = converted
                    else:
                        print(f"  ⚠️  Timestamp too large (milliseconds): {field_name} = {value}")
                elif value >= 946684800:  # Seconds
                    if value <= 253402300799:
                        converted = datetime.fromtimestamp(value).strftime('%Y-%m-%d %H:%M:%S')
                        record[field_name] = converted
                    else:
                        print(f"  ⚠️  Timestamp too large (seconds): {field_name} = {value}")
                else:
                    print(f"  ⚠️  Timestamp too small: {field_name} = {value}")
            except Exception as e:
                print(f"  ❌ Timestamp conversion error: {field_name} = {value}, error: {e}")
    
    return record

def validate_fields(record: Dict, table_columns: List[str]) -> Dict:
    """Validate record fields against table schema."""
    record_fields = set(record.keys()) - {'_kafka_offset', '_raw_message'}
    table_columns_set = set(table_columns)
    
    extra_fields = record_fields - table_columns_set
    missing_fields = table_columns_set - record_fields
    matching_fields = record_fields & table_columns_set
    
    return {
        'matching_fields': list(matching_fields),
        'extra_fields': list(extra_fields),
        'missing_fields': list(missing_fields),
        'field_count_match': len(record_fields) == len(table_columns_set)
    }

def adapt_for_postgres(record: Dict, table_columns: List[str]) -> List[Any]:
    """Adapt values for PostgreSQL insertion."""
    adapted_values = []
    
    for col in table_columns:
        if col in record:
            value = record[col]
            if value is None:
                adapted_values.append(None)
            elif isinstance(value, Decimal):
                adapted_values.append(float(value))
            elif isinstance(value, dict):
                adapted_values.append(json.dumps(value))
            elif isinstance(value, (list, tuple)):
                adapted_values.append(json.dumps(value))
            else:
                adapted_values.append(value)
        else:
            adapted_values.append(None)  # Missing field -> NULL
    
    return adapted_values

def generate_sql(table_columns: List[str], schema_name: str, table_name: str, 
                primary_keys: List[str]) -> str:
    """Generate SQL INSERT statement with conflict resolution."""
    columns_str = ", ".join(f'"{col}"' for col in table_columns)
    placeholders = ", ".join(["%s"] * len(table_columns))
    
    if primary_keys:
        pk_columns = ", ".join(f'"{pk}"' for pk in primary_keys)
        update_columns = [col for col in table_columns if col not in primary_keys]
        update_set = ", ".join(f'"{col}" = EXCLUDED."{col}"' for col in update_columns)
        
        has_op_ts = 'op_ts' in table_columns
        
        if has_op_ts:
            query = f'''
            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT ({pk_columns}) DO UPDATE SET
            {update_set}
            WHERE "{schema_name}"."{table_name}".op_ts < EXCLUDED.op_ts
            '''
        else:
            query = f'''
            INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
            VALUES ({placeholders})
            ON CONFLICT ({pk_columns}) DO UPDATE SET
            {update_set}
            '''
    else:
        query = f'''
        INSERT INTO "{schema_name}"."{table_name}" ({columns_str})
        VALUES ({placeholders})
        '''
    
    return query.strip()

def test_database_simulation(adapted_values: List[Any], table_columns: List[str]) -> bool:
    """Test database operations using SQLite."""
    try:
        # Create temporary SQLite database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            conn = sqlite3.connect(tmp_file.name)
            cursor = conn.cursor()
            
            # Create table
            column_defs = []
            for col in table_columns:
                if col in ['BATCH_ID', 'TXN_REF_NO']:
                    column_defs.append(f'"{col}" INTEGER')
                elif 'DATETIME' in col or 'DATE' in col:
                    column_defs.append(f'"{col}" TEXT')
                elif 'AMNT' in col or 'RATE' in col:
                    column_defs.append(f'"{col}" REAL')
                else:
                    column_defs.append(f'"{col}" TEXT')
            
            create_sql = f"CREATE TABLE test_table ({', '.join(column_defs)})"
            cursor.execute(create_sql)
            
            # Insert data
            placeholders = ', '.join(['?' for _ in table_columns])
            insert_sql = f"INSERT INTO test_table VALUES ({placeholders})"
            cursor.execute(insert_sql, adapted_values)
            
            # Verify insertion
            cursor.execute("SELECT COUNT(*) FROM test_table")
            count = cursor.fetchone()[0]
            
            conn.close()
            return count == 1
            
    except Exception as e:
        print(f"  ❌ Database simulation failed: {e}")
        return False

def run_pipeline_test():
    """Run the complete pipeline test."""
    print("🧪 MSK Consumer Pipeline Test (Standalone)")
    print("=" * 45)
    
    # Configuration
    schema_name = "test_schema"
    table_name = "payment_transactions"
    
    # Table schema matching sample data
    table_columns = [
        'BATCH_ID', 'FILE_REF_NO', 'TXN_REF_NO', 'PYMNT_AMNT', 'PYMNT_VALUE_DATE',
        'PYMNT_CURR', 'EX_RATE', 'STATUS', 'DEBIT_ACC_NO', 'ORDER_CUST_NAME',
        'BENE_NAME', 'BENE_ACC_NO', 'PROCESS_DATETIME', 'MAKER_DATE', 'CHECKER_DATE',
        'TRANSACTION_TYPE', 'DEBIT_CCY', 'PYMNT_DEBIT_AMNT', 'CID', 'op_ts'
    ]
    
    primary_keys = ['BATCH_ID', 'TXN_REF_NO']
    timestamp_columns = ['PYMNT_VALUE_DATE', 'PROCESS_DATETIME', 'MAKER_DATE', 'CHECKER_DATE']
    
    # Load sample data
    print("📄 Loading sample data...")
    sample_data = load_sample_data()
    if not sample_data:
        return False
    
    print(f"  ✅ Loaded {len(sample_data)} fields")
    
    try:
        # Step 1: Process Avro unions
        print("\n🔄 Step 1: Processing Avro union types...")
        processed_data = process_avro_unions(sample_data)
        print(f"  ✅ Processed {len(processed_data)} fields")
        
        # Step 2: Convert timestamps
        print("\n🔄 Step 2: Converting timestamps...")
        processed_data = convert_timestamps(processed_data, timestamp_columns)
        
        # Step 3: Validate fields
        print("\n🔄 Step 3: Validating fields...")
        validation = validate_fields(processed_data, table_columns)
        print(f"  📊 {len(validation['matching_fields'])} matching, "
              f"{len(validation['extra_fields'])} extra, {len(validation['missing_fields'])} missing")
        
        # Step 4: Adapt for PostgreSQL
        print("\n🔄 Step 4: Adapting for PostgreSQL...")
        adapted_values = adapt_for_postgres(processed_data, table_columns)
        print(f"  ✅ Prepared {len(adapted_values)} values")
        
        # Step 5: Generate SQL
        print("\n🔄 Step 5: Generating SQL...")
        sql_query = generate_sql(table_columns, schema_name, table_name, primary_keys)
        print(f"  ✅ Generated SQL query ({len(sql_query)} characters)")
        
        # Step 6: Test database simulation
        print("\n🔄 Step 6: Testing database simulation...")
        db_success = test_database_simulation(adapted_values, table_columns)
        print(f"  ✅ Database test: {'SUCCESS' if db_success else 'FAILED'}")
        
        # Summary
        print(f"\n📊 Test Results:")
        print(f"  ✅ Avro processing: SUCCESS")
        print(f"  ✅ Timestamp conversion: SUCCESS")
        print(f"  ✅ Field validation: {len(validation['matching_fields'])}/{len(table_columns)} fields matched")
        print(f"  ✅ PostgreSQL adaptation: SUCCESS")
        print(f"  ✅ SQL generation: SUCCESS")
        print(f"  ✅ Database simulation: {'SUCCESS' if db_success else 'FAILED'}")
        
        print(f"\n🔍 Sample SQL (first 150 chars):")
        print(f"  {sql_query[:150]}...")
        
        # Show sample processed data
        print(f"\n🔍 Sample processed fields:")
        for field, value in list(processed_data.items())[:5]:
            print(f"  {field}: {str(value)[:50]}{'...' if len(str(value)) > 50 else ''} ({type(value).__name__})")
        
        overall_success = db_success and len(validation['matching_fields']) > 0
        print(f"\n🎯 Overall Result: {'SUCCESS' if overall_success else 'SUCCESS WITH WARNINGS'}")
        
        return overall_success
        
    except Exception as e:
        print(f"\n❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_pipeline_test()
    exit(0 if success else 1)
