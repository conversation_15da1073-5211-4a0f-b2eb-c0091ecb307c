#!/usr/bin/env python3
"""
Pytest-based test suite using pytest-postgresql for real PostgreSQL testing.
This provides structured testing with fixtures and better test organization.

Installation:
    pip install -r requirements-test.txt

Usage:
    # Run all tests
    pytest test_pytest_postgres.py -v

    # Run specific test
    pytest test_pytest_postgres.py::test_numeric_precision -v

    # Run with coverage
    pytest test_pytest_postgres.py --cov=postgres_utils --cov-report=html
"""

import json
import pytest
import sys
import os
from decimal import Decimal
from datetime import datetime
from typing import Dict, List, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import testing dependencies
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

# Import pytest-postgresql
try:
    from pytest_postgresql import factories
    PYTEST_POSTGRESQL_AVAILABLE = True
except ImportError:
    PYTEST_POSTGRESQL_AVAILABLE = False

# Skip all tests if dependencies not available
pytestmark = pytest.mark.skipif(
    not (PSYCOPG2_AVAILABLE and PYTEST_POSTGRESQL_AVAILABLE),
    reason="psycopg2 and pytest-postgresql required"
)

# Create PostgreSQL factory
postgresql_proc = factories.postgresql_proc(
    port=None,
    unixsocketdir='/tmp'
)
postgresql = factories.postgresql('postgresql_proc')

@pytest.fixture
def sample_data():
    """Load sample data for testing."""
    try:
        with open('pyhub_sample.txt', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        pytest.skip("pyhub_sample.txt not found")

@pytest.fixture
def processed_sample_data(sample_data):
    """Process sample data to extract Avro union values."""
    processed = {}
    
    for field_name, value in sample_data.items():
        if isinstance(value, dict):
            if 'bytes' in value:
                try:
                    processed[field_name] = Decimal(str(value['bytes']))
                except:
                    processed[field_name] = value['bytes']
            elif 'string' in value:
                processed[field_name] = value['string']
            elif 'long' in value:
                processed[field_name] = value['long']
            elif 'int' in value:
                processed[field_name] = value['int']
            else:
                processed[field_name] = value
        else:
            processed[field_name] = value
    
    return processed

@pytest.fixture
def test_table(postgresql):
    """Create test table with PostgreSQL types and constraints."""
    cursor = postgresql.cursor()
    
    # Create schema
    cursor.execute("CREATE SCHEMA IF NOT EXISTS test_msk")
    
    # Create table
    create_table_sql = '''
    CREATE TABLE test_msk.payment_transactions (
        batch_id INTEGER NOT NULL,
        file_ref_no NUMERIC(20,0),
        txn_ref_no BIGINT NOT NULL,
        pymnt_amnt NUMERIC(15,9) CHECK (pymnt_amnt >= 0),
        pymnt_value_date TIMESTAMP WITHOUT TIME ZONE,
        pymnt_curr VARCHAR(3) CHECK (LENGTH(pymnt_curr) = 3),
        ex_rate NUMERIC(15,10) CHECK (ex_rate > 0),
        status VARCHAR(10),
        debit_acc_no VARCHAR(50),
        order_cust_name VARCHAR(255),
        bene_name VARCHAR(255),
        bene_acc_no VARCHAR(50),
        process_datetime TIMESTAMP WITHOUT TIME ZONE,
        maker_date TIMESTAMP WITHOUT TIME ZONE,
        checker_date TIMESTAMP WITHOUT TIME ZONE,
        transaction_type VARCHAR(10),
        debit_ccy VARCHAR(3),
        pymnt_debit_amnt NUMERIC(15,9),
        cid VARCHAR(20),
        op_ts TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        
        PRIMARY KEY (batch_id, txn_ref_no),
        
        CONSTRAINT valid_currencies 
            CHECK (pymnt_curr ~ '^[A-Z]{3}$'),
        CONSTRAINT valid_status_values 
            CHECK (status IN ('10', '20', '30', '40', '50'))
    )
    '''
    
    cursor.execute(create_table_sql)
    postgresql.commit()
    
    yield cursor
    
    # Cleanup
    cursor.execute("DROP SCHEMA test_msk CASCADE")
    postgresql.commit()

def test_postgresql_connection(postgresql):
    """Test that PostgreSQL connection is working."""
    cursor = postgresql.cursor()
    cursor.execute("SELECT version()")
    version = cursor.fetchone()[0]
    
    assert "PostgreSQL" in version
    print(f"Connected to: {version.split(',')[0]}")

def test_numeric_precision(test_table, postgresql):
    """Test NUMERIC precision preservation."""
    cursor = test_table
    
    # Test high precision decimal
    test_amount = Decimal('1000.*********')
    
    cursor.execute('''
        INSERT INTO test_msk.payment_transactions 
        (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status)
        VALUES (%s, %s, %s, %s, %s, %s)
    ''', (1, 1, test_amount, 'USD', 'USD', '10'))
    
    postgresql.commit()
    
    # Retrieve and verify precision
    cursor.execute('''
        SELECT pymnt_amnt FROM test_msk.payment_transactions 
        WHERE batch_id = 1 AND txn_ref_no = 1
    ''')
    
    result = cursor.fetchone()[0]
    
    # Check exact precision preservation
    assert str(result) == str(test_amount)
    assert isinstance(result, Decimal)

def test_timestamp_operations(test_table, postgresql):
    """Test PostgreSQL timestamp operations."""
    cursor = test_table
    
    # Insert record with timestamp
    now = datetime.now()
    cursor.execute('''
        INSERT INTO test_msk.payment_transactions 
        (batch_id, txn_ref_no, pymnt_value_date, process_datetime, op_ts,
         pymnt_curr, debit_ccy, status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
    ''', (2, 1, now, now, now, 'USD', 'USD', '10'))
    
    postgresql.commit()
    
    # Test timestamp functions
    cursor.execute('''
        SELECT 
            pymnt_value_date,
            EXTRACT(EPOCH FROM op_ts) as epoch_seconds,
            op_ts + INTERVAL '1 day' as tomorrow,
            AGE(CURRENT_TIMESTAMP, op_ts) as age
        FROM test_msk.payment_transactions 
        WHERE batch_id = 2
    ''')
    
    result = cursor.fetchone()
    
    assert result[0] is not None  # timestamp stored
    assert isinstance(result[1], float)  # epoch extraction
    assert result[2] is not None  # date arithmetic
    assert result[3] is not None  # age calculation

def test_constraint_enforcement(test_table, postgresql):
    """Test PostgreSQL constraint enforcement."""
    cursor = test_table
    
    # Test VARCHAR length constraint
    with pytest.raises(psycopg2.Error):
        cursor.execute('''
            INSERT INTO test_msk.payment_transactions 
            (batch_id, txn_ref_no, pymnt_curr, debit_ccy, status)
            VALUES (3, 1, 'TOOLONG', 'USD', '10')
        ''')
    
    postgresql.rollback()
    
    # Test CHECK constraint (negative amount)
    with pytest.raises(psycopg2.Error):
        cursor.execute('''
            INSERT INTO test_msk.payment_transactions 
            (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status)
            VALUES (3, 2, -100.50, 'USD', 'USD', '10')
        ''')
    
    postgresql.rollback()

def test_on_conflict_syntax(test_table, postgresql):
    """Test PostgreSQL ON CONFLICT DO UPDATE syntax."""
    cursor = test_table
    
    # Insert initial record
    cursor.execute('''
        INSERT INTO test_msk.payment_transactions 
        (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status)
        VALUES (4, 1, 1000.00, 'USD', 'USD', '10')
    ''')
    
    postgresql.commit()
    
    # Test ON CONFLICT DO UPDATE
    cursor.execute('''
        INSERT INTO test_msk.payment_transactions 
        (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status, op_ts)
        VALUES (4, 1, 2000.*********, 'EUR', 'EUR', '20', CURRENT_TIMESTAMP)
        ON CONFLICT (batch_id, txn_ref_no) 
        DO UPDATE SET 
            pymnt_amnt = EXCLUDED.pymnt_amnt,
            pymnt_curr = EXCLUDED.pymnt_curr,
            debit_ccy = EXCLUDED.debit_ccy,
            status = EXCLUDED.status,
            op_ts = EXCLUDED.op_ts
        WHERE test_msk.payment_transactions.op_ts < EXCLUDED.op_ts
    ''')
    
    postgresql.commit()
    
    # Verify the update
    cursor.execute('''
        SELECT pymnt_amnt, pymnt_curr, status 
        FROM test_msk.payment_transactions 
        WHERE batch_id = 4 AND txn_ref_no = 1
    ''')
    
    result = cursor.fetchone()
    
    assert str(result[0]) == '2000.*********'  # Precision preserved
    assert result[1] == 'EUR'  # Currency updated
    assert result[2] == '20'   # Status updated

def test_postgres_utils_integration(test_table, postgresql, processed_sample_data):
    """Test integration with postgres_utils functions."""
    try:
        from postgres_utils import validate_record_fields
        from postgres_batch_utils import adapt_value_for_postgres
        
        table_columns = [
            'batch_id', 'file_ref_no', 'txn_ref_no', 'pymnt_amnt', 'pymnt_value_date',
            'pymnt_curr', 'ex_rate', 'status', 'debit_acc_no', 'order_cust_name',
            'bene_name', 'bene_acc_no', 'process_datetime', 'maker_date', 'checker_date',
            'transaction_type', 'debit_ccy', 'pymnt_debit_amnt', 'cid', 'op_ts'
        ]
        
        # Test field validation
        validation_result = validate_record_fields(
            processed_sample_data, table_columns, 'test_msk', 'payment_transactions'
        )
        
        assert 'matching_fields' in validation_result
        assert 'extra_fields' in validation_result
        assert 'missing_fields' in validation_result
        assert len(validation_result['matching_fields']) > 0
        
        # Test value adaptation
        adapted_values = []
        for col in table_columns:
            if col in processed_sample_data:
                adapted = adapt_value_for_postgres(processed_sample_data[col])
                adapted_values.append(adapted)
            else:
                adapted_values.append(None)
        
        assert len(adapted_values) == len(table_columns)
        
    except ImportError:
        pytest.skip("postgres_utils not available")

def test_sample_data_processing(test_table, postgresql, processed_sample_data):
    """Test processing of actual sample data."""
    cursor = test_table
    
    # Extract key fields from sample data
    batch_id = processed_sample_data.get('BATCH_ID', 1)
    txn_ref_no = processed_sample_data.get('TXN_REF_NO', 1)
    pymnt_amnt = processed_sample_data.get('PYMNT_AMNT', Decimal('1000.0'))
    pymnt_curr = processed_sample_data.get('PYMNT_CURR', 'USD')
    
    # Insert sample data
    cursor.execute('''
        INSERT INTO test_msk.payment_transactions 
        (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, debit_ccy, status)
        VALUES (%s, %s, %s, %s, %s, %s)
    ''', (batch_id, txn_ref_no, pymnt_amnt, pymnt_curr, 'USD', '10'))
    
    postgresql.commit()
    
    # Verify insertion
    cursor.execute('''
        SELECT batch_id, txn_ref_no, pymnt_amnt, pymnt_curr
        FROM test_msk.payment_transactions 
        WHERE batch_id = %s AND txn_ref_no = %s
    ''', (batch_id, txn_ref_no))
    
    result = cursor.fetchone()
    
    assert result[0] == batch_id
    assert result[1] == txn_ref_no
    assert result[2] == pymnt_amnt
    assert result[3] == pymnt_curr

if __name__ == "__main__":
    # Run pytest programmatically
    pytest.main([__file__, "-v"])
