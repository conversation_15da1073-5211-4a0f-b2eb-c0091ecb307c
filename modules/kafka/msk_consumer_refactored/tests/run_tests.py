#!/usr/bin/env python3
"""
Test runner for MSK Consumer refactored code.
Runs all unit tests with proper mocking of external dependencies.
"""

import unittest
import sys
import os
from unittest.mock import MagicMock

def setup_mocks():
    """Set up mocks for external dependencies before importing test modules."""

    # Mock external dependencies that aren't available in test environment
    external_modules = [
        'confluent_kafka',
        'confluent_kafka.admin',
        'confluent_kafka.schema_registry',
        'psycopg2',
        'psycopg2.pool',
        'psycopg2.extras',
        'fastavro',
        'psutil',
        'setproctitle',
        'boto3',
        'botocore',
    ]

    for module in external_modules:
        sys.modules[module] = MagicMock()

    # Special handling for botocore.exceptions to provide proper exception classes
    class MockClientError(Exception):
        pass

    mock_botocore_exceptions = MagicMock()
    mock_botocore_exceptions.ClientError = MockClientError
    sys.modules['botocore.exceptions'] = mock_botocore_exceptions

    # Mock boto3 with proper structure for secrets
    mock_boto3 = MagicMock()
    mock_client = MagicMock()
    mock_response = {
        'SecretString': '{"msk-bootstrap": "test-bootstrap", "schema-registry": "test-registry", "kafka_target_db": "test-db", "dbt_user": "test-user", "dbt_password": "test-pass", "dbt_host": "test-host"}'
    }
    mock_client.get_secret_value.return_value = mock_response
    mock_boto3.client.return_value = mock_client
    sys.modules['boto3'] = mock_boto3

    # Mock confluent_kafka with proper structure
    mock_confluent_kafka = MagicMock()
    mock_confluent_kafka.Consumer = MagicMock
    mock_confluent_kafka.Producer = MagicMock
    mock_confluent_kafka.admin = MagicMock()
    mock_confluent_kafka.admin.AdminClient = MagicMock
    mock_confluent_kafka.admin.NewTopic = MagicMock
    sys.modules['confluent_kafka'] = mock_confluent_kafka

    # Mock fastavro
    mock_fastavro = MagicMock()
    mock_fastavro.parse_schema = MagicMock(return_value=MagicMock())
    mock_fastavro.schemaless_reader = MagicMock(return_value={'test': 'data'})
    sys.modules['fastavro'] = mock_fastavro

def discover_and_run_tests():
    """Discover and run all tests in the tests directory."""

    # Get the directory containing this script
    test_dir = os.path.dirname(os.path.abspath(__file__))

    # Discover all test files
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern='test_*.py')

    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Return success/failure
    return result.wasSuccessful()

def main():
    """Main test runner function."""
    print("Setting up mocks for external dependencies...")
    setup_mocks()

    print("\nDiscovering and running tests...")
    success = discover_and_run_tests()

    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
