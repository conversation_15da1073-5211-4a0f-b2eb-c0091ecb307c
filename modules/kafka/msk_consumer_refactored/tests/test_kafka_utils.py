#!/usr/bin/env python3
"""
Unit tests for kafka_utils.py module.
Tests Kafka consumer functionality, DLQ management, and message processing.
"""

import unittest
from unittest.mock import patch, MagicMock, call
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestKafkaUtils(unittest.TestCase):
    """Test cases for kafka_utils module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            "table_mapping": {
                "schema.table1": {
                    "topic": "test-topic",
                    "group_id": "test-group",
                    "write_batch_size": 100,
                    "batch_wait": 2.0,
                    "local_schema_file": "test_schema.avsc"
                }
            }
        }
        
        self.test_schema = {
            "type": "record",
            "name": "TestRecord",
            "fields": [
                {"name": "id", "type": "int"},
                {"name": "name", "type": "string"}
            ]
        }

    def test_get_dlq_topic_name(self):
        """Test DLQ topic name generation."""
        from kafka_utils import get_dlq_topic_name
        
        result = get_dlq_topic_name("test-topic")
        
        self.assertEqual(result, "test-topic-DLQ")

    @patch('config_loader.ENABLE_DLQ', True)
    def test_send_to_dlq_enabled(self):
        """Test DLQ message sending when enabled."""
        from kafka_utils import send_to_dlq

        mock_producer = MagicMock()
        test_message = b'test_raw_message'

        send_to_dlq(test_message, "test-topic", mock_producer)

        mock_producer.produce.assert_called_once()
        call_args = mock_producer.produce.call_args
        self.assertEqual(call_args[1]['value'], test_message)

    @patch('config_loader.ENABLE_DLQ', False)
    def test_send_to_dlq_disabled(self):
        """Test DLQ message sending when disabled."""
        from kafka_utils import send_to_dlq

        mock_producer = MagicMock()

        send_to_dlq(b'test_message', "test-topic", mock_producer)

        mock_producer.produce.assert_not_called()

    @patch('kafka_utils.AdminClient')
    def test_ensure_dlq_topic_exists_topic_exists(self, mock_admin_client):
        """Test DLQ topic creation when topic already exists."""
        from kafka_utils import ensure_dlq_topic_exists
        
        mock_client = MagicMock()
        mock_admin_client.return_value = mock_client
        
        mock_metadata = MagicMock()
        mock_metadata.topics = {"test-topic-DLQ": MagicMock()}
        mock_client.list_topics.return_value = mock_metadata
        
        ensure_dlq_topic_exists("test-topic")
        
        mock_client.list_topics.assert_called_once()
        mock_client.create_topics.assert_not_called()

    @patch('kafka_utils.AdminClient')
    def test_ensure_dlq_topic_exists_create_topic(self, mock_admin_client):
        """Test DLQ topic creation when topic doesn't exist."""
        from kafka_utils import ensure_dlq_topic_exists
        
        mock_client = MagicMock()
        mock_admin_client.return_value = mock_client
        
        mock_metadata = MagicMock()
        mock_metadata.topics = {}  # Topic doesn't exist
        mock_client.list_topics.return_value = mock_metadata
        
        # Mock successful topic creation
        mock_future = MagicMock()
        mock_future.result.return_value = None
        mock_client.create_topics.return_value = {"test-topic-DLQ": mock_future}
        
        ensure_dlq_topic_exists("test-topic")
        
        mock_client.create_topics.assert_called_once()
        mock_future.result.assert_called_once()

    @patch('kafka_utils.ENABLE_DLQ', False)
    def test_ensure_dlq_topic_exists_disabled(self):
        """Test DLQ topic creation when DLQ is disabled."""
        from kafka_utils import ensure_dlq_topic_exists
        
        with patch('kafka_utils.AdminClient') as mock_admin_client:
            ensure_dlq_topic_exists("test-topic")
            
            mock_admin_client.assert_not_called()

    @patch('kafka_utils.set_process_name')
    @patch('kafka_utils.get_table_columns')
    @patch('kafka_utils.get_local_schema')
    @patch('kafka_utils.Consumer')
    @patch('kafka_utils.Producer')
    @patch('kafka_utils.ensure_dlq_topic_exists')
    def test_consume_messages_table_not_found(self, mock_ensure_dlq, mock_producer, 
                                            mock_consumer, mock_get_schema, 
                                            mock_get_columns, mock_set_process):
        """Test consume_messages with table not in configuration."""
        from kafka_utils import consume_messages
        
        with patch('kafka_utils.logging') as mock_logging:
            consume_messages("nonexistent.table", False, self.test_config)
            
            mock_logging.error.assert_called_once()
            mock_set_process.assert_not_called()

    @patch('kafka_utils.set_process_name')
    @patch('kafka_utils.get_table_columns')
    @patch('kafka_utils.get_local_schema')
    @patch('kafka_utils.Consumer')
    @patch('kafka_utils.Producer')
    @patch('kafka_utils.ensure_dlq_topic_exists')
    def test_consume_messages_local_schema_success(self, mock_ensure_dlq, mock_producer,
                                                 mock_consumer, mock_get_schema,
                                                 mock_get_columns, mock_set_process):
        """Test consume_messages with local schema."""
        from kafka_utils import consume_messages
        
        mock_set_process.return_value = "test_process"
        mock_get_columns.return_value = ["id", "name"]
        mock_get_schema.return_value = self.test_schema
        
        mock_consumer_instance = MagicMock()
        mock_consumer.return_value = mock_consumer_instance
        
        # Mock message polling to return None (no messages)
        mock_consumer_instance.poll.return_value = None
        
        with patch('kafka_utils.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2021, 1, 1, 12, 0, 0)
            
            # This will run indefinitely, so we need to stop it
            with patch('kafka_utils.insert_batch_into_postgres'):
                try:
                    consume_messages("schema.table1", True, self.test_config)
                except KeyboardInterrupt:
                    pass

        mock_get_schema.assert_called_once_with("test_schema.avsc")
        mock_consumer_instance.subscribe.assert_called_once_with(["test-topic"])

    @patch('kafka_utils.set_process_name')
    @patch('kafka_utils.get_table_columns')
    @patch('kafka_utils.fetch_schema_from_registry')
    @patch('kafka_utils.Consumer')
    @patch('kafka_utils.Producer')
    @patch('kafka_utils.ensure_dlq_topic_exists')
    def test_consume_messages_registry_schema_success(self, mock_ensure_dlq, mock_producer,
                                                    mock_consumer, mock_fetch_schema,
                                                    mock_get_columns, mock_set_process):
        """Test consume_messages with schema registry."""
        from kafka_utils import consume_messages
        
        mock_set_process.return_value = "test_process"
        mock_get_columns.return_value = ["id", "name"]
        mock_fetch_schema.return_value = self.test_schema
        
        mock_consumer_instance = MagicMock()
        mock_consumer.return_value = mock_consumer_instance
        mock_consumer_instance.poll.return_value = None
        
        with patch('kafka_utils.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime(2021, 1, 1, 12, 0, 0)
            
            with patch('kafka_utils.insert_batch_into_postgres'):
                try:
                    consume_messages("schema.table1", False, self.test_config)
                except KeyboardInterrupt:
                    pass

        mock_fetch_schema.assert_called_once_with("test-topic")

    @patch('kafka_utils.set_process_name')
    @patch('kafka_utils.get_table_columns')
    @patch('kafka_utils.get_local_schema')
    def test_consume_messages_local_schema_missing_file(self, mock_get_schema, 
                                                      mock_get_columns, mock_set_process):
        """Test consume_messages with missing local schema file config."""
        from kafka_utils import consume_messages
        
        config_without_schema = {
            "table_mapping": {
                "schema.table1": {
                    "topic": "test-topic",
                    "group_id": "test-group"
                    # Missing local_schema_file
                }
            }
        }
        
        with patch('kafka_utils.logging') as mock_logging:
            consume_messages("schema.table1", True, config_without_schema)
            
            mock_logging.error.assert_called()
            mock_get_schema.assert_not_called()

    @patch('kafka_utils.set_process_name')
    @patch('kafka_utils.get_table_columns')
    @patch('kafka_utils.get_local_schema')
    def test_consume_messages_schema_load_failure(self, mock_get_schema,
                                                mock_get_columns, mock_set_process):
        """Test consume_messages with schema loading failure."""
        from kafka_utils import consume_messages
        
        mock_get_schema.return_value = None  # Schema loading failed
        
        with patch('kafka_utils.logging') as mock_logging:
            consume_messages("schema.table1", True, self.test_config)
            
            mock_logging.error.assert_called()


if __name__ == '__main__':
    unittest.main()
