#!/usr/bin/env python3
"""
Unit tests for secrets_manager.py module.
Tests credential caching and retrieval functionality.
"""

import unittest
from unittest.mock import patch, MagicMock
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestSecretsManager(unittest.TestCase):
    """Test cases for secrets_manager module."""

    def setUp(self):
        """Set up test fixtures."""
        self.kafka_credentials = {
            "msk-bootstrap": "test-kafka:9092",
            "schema-registry": "http://test-schema:8081"
        }
        self.postgres_credentials = {
            "kafka_target_db": "test_db",
            "dbt_user": "test_user",
            "dbt_password": "test_pass",
            "dbt_host": "test-host"
        }

    def tearDown(self):
        """Clean up after each test."""
        # Reset the global variables
        import secrets_manager
        secrets_manager._kafka_credentials = None
        secrets_manager._postgres_credentials = None

    @patch('secrets_manager.get_secret')
    def test_get_kafka_credentials_first_call(self, mock_get_secret):
        """Test first call to get_kafka_credentials caches the result."""
        mock_get_secret.return_value = self.kafka_credentials

        from secrets_manager import get_kafka_credentials

        # First call
        result = get_kafka_credentials()

        # Assertions
        self.assertEqual(result, self.kafka_credentials)
        mock_get_secret.assert_called_once_with('msk-uat', 'me-central-1')

    @patch('secrets_manager.get_secret')
    def test_get_kafka_credentials_cached(self, mock_get_secret):
        """Test subsequent calls use cached credentials."""
        mock_get_secret.return_value = self.kafka_credentials

        from secrets_manager import get_kafka_credentials

        # First call
        result1 = get_kafka_credentials()
        # Second call
        result2 = get_kafka_credentials()

        # Assertions
        self.assertEqual(result1, self.kafka_credentials)
        self.assertEqual(result2, self.kafka_credentials)
        # get_secret should only be called once due to caching
        mock_get_secret.assert_called_once_with('msk-uat', 'me-central-1')

    @patch('secrets_manager.get_secret')
    def test_get_postgres_credentials_first_call(self, mock_get_secret):
        """Test first call to get_postgres_credentials caches the result."""
        mock_get_secret.return_value = self.postgres_credentials

        from secrets_manager import get_postgres_credentials

        # First call
        result = get_postgres_credentials()

        # Assertions
        self.assertEqual(result, self.postgres_credentials)
        mock_get_secret.assert_called_once_with('cdp-rds-postgres', 'me-central-1')

    @patch('secrets_manager.get_secret')
    def test_get_postgres_credentials_cached(self, mock_get_secret):
        """Test subsequent calls use cached credentials."""
        mock_get_secret.return_value = self.postgres_credentials

        from secrets_manager import get_postgres_credentials

        # First call
        result1 = get_postgres_credentials()
        # Second call
        result2 = get_postgres_credentials()

        # Assertions
        self.assertEqual(result1, self.postgres_credentials)
        self.assertEqual(result2, self.postgres_credentials)
        # get_secret should only be called once due to caching
        mock_get_secret.assert_called_once_with('cdp-rds-postgres', 'me-central-1')

    @patch('secrets_manager.get_secret')
    def test_both_credentials_independent_caching(self, mock_get_secret):
        """Test that Kafka and Postgres credentials are cached independently."""
        # Set up mock to return different values based on secret name
        def mock_get_secret_side_effect(secret_name, region):
            if secret_name == 'msk-uat':
                return self.kafka_credentials
            elif secret_name == 'cdp-rds-postgres':
                return self.postgres_credentials
            return {}

        mock_get_secret.side_effect = mock_get_secret_side_effect

        from secrets_manager import get_kafka_credentials, get_postgres_credentials

        # Get both types of credentials
        kafka_result = get_kafka_credentials()
        postgres_result = get_postgres_credentials()

        # Get them again to test caching
        kafka_result2 = get_kafka_credentials()
        postgres_result2 = get_postgres_credentials()

        # Assertions - check that we got the right credentials for each type
        self.assertEqual(kafka_result, self.kafka_credentials)
        self.assertEqual(postgres_result, self.postgres_credentials)
        self.assertEqual(kafka_result2, self.kafka_credentials)
        self.assertEqual(postgres_result2, self.postgres_credentials)

        # Each secret should be fetched exactly once
        self.assertEqual(mock_get_secret.call_count, 2)
        mock_get_secret.assert_any_call('msk-uat', 'me-central-1')
        mock_get_secret.assert_any_call('cdp-rds-postgres', 'me-central-1')

    @patch('secrets_manager.get_secret')
    def test_get_secret_failure_handling(self, mock_get_secret):
        """Test handling when get_secret fails."""
        mock_get_secret.return_value = {}  # Empty dict on failure

        from secrets_manager import get_kafka_credentials

        result = get_kafka_credentials()

        # Should return empty dict
        self.assertEqual(result, {})
        mock_get_secret.assert_called_once_with('msk-uat', 'me-central-1')

    def test_module_level_variables_initialization(self):
        """Test that module-level variables are properly initialized."""
        import secrets_manager

        # Variables should start as None
        self.assertIsNone(secrets_manager._kafka_credentials)
        self.assertIsNone(secrets_manager._postgres_credentials)


if __name__ == '__main__':
    unittest.main()
