#!/usr/bin/env python3
"""
Unit tests for get_secrets.py module.
Tests AWS Secrets Manager integration functionality.
"""

import unittest
from unittest.mock import patch, MagicMock
import json
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestGetSecrets(unittest.TestCase):
    """Test cases for get_secrets module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_secret_data = {
            "msk-bootstrap": "test-bootstrap-server:9092",
            "schema-registry": "http://test-schema-registry:8081",
            "kafka_target_db": "test_database",
            "dbt_user": "test_user",
            "dbt_password": "test_password",
            "dbt_host": "test-host.amazonaws.com"
        }
        self.secret_string = json.dumps(self.test_secret_data)

    @patch('boto3.client')
    def test_get_secret_success(self, mock_boto_client):
        """Test successful secret retrieval."""
        # Mock the boto3 client and response
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        mock_client.get_secret_value.return_value = {
            'SecretString': self.secret_string
        }

        # Import after mocking
        from get_secrets import get_secret

        # Test the function
        result = get_secret('test-secret', 'us-east-1')

        # Assertions
        self.assertEqual(result, self.test_secret_data)
        mock_boto_client.assert_called_once_with("secretsmanager", region_name="us-east-1")
        mock_client.get_secret_value.assert_called_once_with(SecretId='test-secret')

    @patch('boto3.client')
    def test_get_secret_no_secret_string(self, mock_boto_client):
        """Test handling of secret without SecretString."""
        # Mock the boto3 client and response without SecretString
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        mock_client.get_secret_value.return_value = {
            'SecretBinary': b'binary-data'
        }

        from get_secrets import get_secret

        # Test the function - should raise ValueError
        with self.assertRaises(ValueError) as context:
            get_secret('test-secret', 'us-east-1')

        self.assertIn("Secret is not in string format", str(context.exception))

    @patch('boto3.client')
    @patch('get_secrets.logging')
    def test_get_secret_client_error(self, mock_logging, mock_boto_client):
        """Test handling of ClientError from AWS."""
        # Create a proper ClientError mock
        class MockClientError(Exception):
            def __init__(self, error_response, operation_name):
                self.response = error_response
                self.operation_name = operation_name
                super().__init__(f"An error occurred ({error_response['Error']['Code']})")

        # Mock the boto3 client to raise ClientError
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        mock_client.get_secret_value.side_effect = MockClientError(
            error_response={'Error': {'Code': 'ResourceNotFoundException'}},
            operation_name='GetSecretValue'
        )

        from get_secrets import get_secret

        # Test the function
        result = get_secret('non-existent-secret', 'us-east-1')

        # Should return empty dict and log error
        self.assertEqual(result, {})
        mock_logging.error.assert_called_once()

    @patch('boto3.client')
    def test_get_secret_invalid_json(self, mock_boto_client):
        """Test handling of invalid JSON in secret."""
        # Mock the boto3 client with invalid JSON
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        mock_client.get_secret_value.return_value = {
            'SecretString': 'invalid-json-string'
        }

        from get_secrets import get_secret

        # Test the function - should raise JSONDecodeError
        with self.assertRaises(json.JSONDecodeError):
            get_secret('test-secret', 'us-east-1')

    @patch('boto3.client')
    def test_get_secret_default_region(self, mock_boto_client):
        """Test default region parameter."""
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        mock_client.get_secret_value.return_value = {
            'SecretString': self.secret_string
        }

        from get_secrets import get_secret

        # Test with default region
        result = get_secret('test-secret')

        # Should use default region us-east-1
        mock_boto_client.assert_called_once_with("secretsmanager", region_name="us-east-1")
        self.assertEqual(result, self.test_secret_data)

    @patch('boto3.client')
    def test_get_secret_custom_region(self, mock_boto_client):
        """Test custom region parameter."""
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        mock_client.get_secret_value.return_value = {
            'SecretString': self.secret_string
        }

        from get_secrets import get_secret

        # Test with custom region
        result = get_secret('test-secret', 'me-central-1')

        # Should use specified region
        mock_boto_client.assert_called_once_with("secretsmanager", region_name="me-central-1")
        self.assertEqual(result, self.test_secret_data)


if __name__ == '__main__':
    unittest.main()
