#!/usr/bin/env python3
"""
Unit tests for postgres_utils.py module.
Tests database operations, schema processing, and decimal/date conversions.
"""

import unittest
from unittest.mock import patch, MagicMock, call
from decimal import Decimal
from datetime import datetime, timedelta
import io
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestPostgresUtils(unittest.TestCase):
    """Test cases for postgres_utils module."""

    def setUp(self):
        """Set up test fixtures."""
        # Clear caches before each test
        import postgres_utils
        postgres_utils.cached_table_columns.clear()
        postgres_utils.cached_primary_keys.clear()

        self.test_schema = {
            "type": "record",
            "name": "TestRecord",
            "fields": [
                {
                    "name": "decimal_field",
                    "type": {
                        "type": "bytes",
                        "logicalType": "decimal",
                        "precision": 10,
                        "scale": 2
                    }
                },
                {
                    "name": "date_field",
                    "type": {
                        "type": "int",
                        "logicalType": "date"
                    }
                }
            ]
        }

    @patch('postgres_utils.get_postgres_credentials')
    @patch('postgres_utils.SimpleConnectionPool')
    def test_initialize_connection_pool(self, mock_pool, mock_get_credentials):
        """Test connection pool initialization."""
        mock_get_credentials.return_value = {
            'kafka_target_db': 'test_db',
            'dbt_user': 'test_user',
            'dbt_password': 'test_pass',
            'dbt_host': 'test_host'
        }

        from postgres_utils import _initialize_connection_pool

        _initialize_connection_pool()

        expected_conn_string = "dbname=test_db user=test_user password=test_pass host=test_host port=5432"
        mock_pool.assert_called_once_with(1, 10, expected_conn_string)

    @patch('postgres_utils._initialize_connection_pool')
    @patch('postgres_utils.connection_pool')
    def test_db_connection_context_manager(self, mock_pool, mock_init):
        """Test database connection context manager."""
        mock_conn = MagicMock()
        mock_pool.getconn.return_value = mock_conn

        from postgres_utils import db_connection

        with db_connection() as conn:
            self.assertEqual(conn, mock_conn)

        mock_init.assert_called_once()
        mock_pool.getconn.assert_called_once()
        mock_pool.putconn.assert_called_once_with(mock_conn)

    @patch('postgres_utils.db_connection')
    def test_get_table_columns_success(self, mock_db_conn):
        """Test successful table column retrieval."""
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_db_conn.return_value.__enter__.return_value = mock_conn

        mock_cursor.fetchall.return_value = [('id',), ('name',), ('created_at',)]

        from postgres_utils import get_table_columns

        result = get_table_columns('test_schema', 'test_table')

        self.assertEqual(result, ['id', 'name', 'created_at'])
        mock_cursor.execute.assert_called_once()

    @patch('postgres_utils.db_connection')
    def test_get_table_columns_cached(self, mock_db_conn):
        """Test that table columns are cached."""
        from postgres_utils import get_table_columns, cached_table_columns

        # Pre-populate cache
        cached_table_columns['test_schema.test_table'] = ['cached_col1', 'cached_col2']

        result = get_table_columns('test_schema', 'test_table')

        self.assertEqual(result, ['cached_col1', 'cached_col2'])
        # Database should not be called
        mock_db_conn.assert_not_called()

    def test_fix_decimal_types(self):
        """Test decimal type fixing in schema."""
        from postgres_utils import fix_decimal_types

        modified_schema, decimal_fields = fix_decimal_types(self.test_schema)

        self.assertIn('decimal_field', decimal_fields)
        self.assertEqual(decimal_fields['decimal_field']['scale'], 2)

    def test_decode_avro_decimal(self):
        """Test Avro decimal decoding."""
        from postgres_utils import decode_avro_decimal

        # Test positive decimal: 123.45 with scale 2
        # 12345 as big-endian bytes
        test_bytes = (12345).to_bytes(4, byteorder='big', signed=True)

        result = decode_avro_decimal(test_bytes, 2)

        self.assertEqual(result, Decimal('123.45'))

    def test_decode_avro_decimal_none(self):
        """Test Avro decimal decoding with None input."""
        from postgres_utils import decode_avro_decimal

        result = decode_avro_decimal(None, 2)

        self.assertIsNone(result)

    def test_decode_variable_scale_decimal_success(self):
        """Test variable scale decimal decoding."""
        from postgres_utils import decode_variable_scale_decimal

        decimal_dict = {
            'scale': 2,
            'value': (12345).to_bytes(4, byteorder='big', signed=True)
        }

        result = decode_variable_scale_decimal(decimal_dict)

        self.assertEqual(result, Decimal('123.45'))

    def test_decode_variable_scale_decimal_scale_zero(self):
        """Test variable scale decimal with scale 0."""
        from postgres_utils import decode_variable_scale_decimal

        decimal_dict = {
            'scale': 0,
            'value': (12345).to_bytes(4, byteorder='big', signed=True)
        }

        result = decode_variable_scale_decimal(decimal_dict)

        self.assertEqual(result, 12345)

    def test_decode_variable_scale_decimal_invalid(self):
        """Test variable scale decimal with invalid input."""
        from postgres_utils import decode_variable_scale_decimal

        invalid_dict = {'invalid': 'data'}

        result = decode_variable_scale_decimal(invalid_dict)

        self.assertEqual(result, invalid_dict)

    def test_convert_decimal_fields(self):
        """Test decimal field conversion in record."""
        from postgres_utils import convert_decimal_fields

        record = {
            'decimal_field': (12345).to_bytes(4, byteorder='big', signed=True),
            'other_field': 'value'
        }
        decimal_fields = {'decimal_field': {'scale': 2}}

        result = convert_decimal_fields(record, decimal_fields)

        self.assertEqual(result['decimal_field'], Decimal('123.45'))
        self.assertEqual(result['other_field'], 'value')

    def test_convert_date_fields(self):
        """Test date field conversion."""
        from postgres_utils import convert_date_fields

        # Test date: days since epoch (1970-01-01)
        days_since_epoch = 18628  # 2021-01-01
        record = {'date_field': days_since_epoch}

        schema = {
            'fields': [
                {
                    'name': 'date_field',
                    'type': {
                        'type': 'int',
                        'logicalType': 'date'
                    }
                }
            ]
        }

        result = convert_date_fields(record, schema)

        self.assertEqual(result['date_field'], '2021-01-01')

    def test_convert_date_fields_timestamp_millis(self):
        """Test timestamp-millis field conversion."""
        from postgres_utils import convert_date_fields
        from datetime import datetime

        # Test timestamp: milliseconds since epoch
        timestamp_millis = *************  # 2021-01-01 00:00:00 UTC
        record = {'timestamp_field': timestamp_millis}

        schema = {
            'fields': [
                {
                    'name': 'timestamp_field',
                    'type': {
                        'type': 'long',
                        'logicalType': 'timestamp-millis'
                    }
                }
            ]
        }

        result = convert_date_fields(record, schema)

        # Convert the expected timestamp to account for local timezone
        expected_dt = datetime.fromtimestamp(timestamp_millis / 1000)
        expected_str = expected_dt.strftime('%Y-%m-%d %H:%M:%S')

        self.assertEqual(result['timestamp_field'], expected_str)

    @patch('postgres_utils.fastavro.schemaless_reader')
    @patch('postgres_utils.parse_schema_with_decimal')
    def test_process_message(self, mock_parse_schema, mock_reader):
        """Test message processing."""
        from postgres_utils import process_message

        mock_parse_schema.return_value = (MagicMock(), {})
        mock_reader.return_value = {'id': 1, 'name': 'test'}

        test_bytes = b'test_message_bytes'

        result = process_message(test_bytes, self.test_schema)

        self.assertIn('id', result)
        self.assertIn('name', result)
        mock_parse_schema.assert_called_once_with(self.test_schema)

    @patch('postgres_utils.fastavro.schemaless_reader')
    @patch('postgres_utils.parse_schema_with_decimal')
    def test_process_message_with_column_types(self, mock_parse_schema, mock_reader):
        """Test message processing with column types for timestamp conversion."""
        from postgres_utils import process_message

        mock_parse_schema.return_value = (MagicMock(), {})
        # Mock a record with a timestamp field
        mock_reader.return_value = {'id': 1, 'timestamp_field': 1640995200000}

        test_bytes = b'test_message_bytes'
        column_types = {'timestamp_field': 'timestamp without time zone'}

        result = process_message(test_bytes, self.test_schema, column_types)

        self.assertIn('id', result)
        self.assertIn('timestamp_field', result)
        # The timestamp should be converted to string format
        self.assertIsInstance(result['timestamp_field'], str)
        mock_parse_schema.assert_called_once_with(self.test_schema)

    @patch('postgres_utils.handle_discarded_message')
    @patch('postgres_utils.fastavro.schemaless_reader')
    @patch('postgres_utils.parse_schema_with_decimal')
    def test_process_message_with_error_handling(self, mock_parse_schema, mock_reader, mock_handle_discarded):
        """Test message processing error handling and discarded message logging."""
        from postgres_utils import process_message

        # Mock schema parsing to raise an error
        mock_parse_schema.side_effect = Exception("Schema parsing failed")

        test_bytes = b'test_message_bytes'
        topic = "test-topic"
        offset = 12345
        discarded_file = "test_discarded.txt"

        with self.assertRaises(Exception) as context:
            process_message(test_bytes, self.test_schema, None, topic, offset, discarded_file)

        self.assertIn("Schema parsing failed", str(context.exception))
        mock_handle_discarded.assert_called_once()

        # Check that handle_discarded_message was called with correct parameters
        call_args = mock_handle_discarded.call_args
        self.assertEqual(call_args[0][1], "Schema parsing failed: Schema parsing failed")  # reason
        self.assertEqual(call_args[0][2], topic)  # topic
        self.assertEqual(call_args[0][3], offset)  # offset
        self.assertEqual(call_args[0][4], discarded_file)  # file path

    @patch('postgres_utils.db_connection')
    @patch('postgres_utils.time.sleep')
    def test_get_primary_key_with_retry(self, mock_sleep, mock_db_conn):
        """Test primary key retrieval with retry logic."""
        mock_conn = MagicMock()
        mock_cursor = MagicMock()
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_db_conn.return_value.__enter__.return_value = mock_conn

        # First call fails, second succeeds
        mock_cursor.execute.side_effect = [Exception("Connection error"), None]
        mock_cursor.fetchall.return_value = [('id',), ('uuid',)]

        from postgres_utils import get_primary_key

        result = get_primary_key('test_schema', 'test_table', max_retries=2, retry_delay=0.1)

        self.assertEqual(result, ['id', 'uuid'])
        self.assertEqual(mock_cursor.execute.call_count, 2)
        mock_sleep.assert_called_once()


if __name__ == '__main__':
    unittest.main()
