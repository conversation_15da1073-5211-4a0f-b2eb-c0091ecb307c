#!/usr/bin/env python3
"""
Unit tests for config_loader.py module.
Tests configuration loading, schema management, and utility functions.
"""

import unittest
from unittest.mock import patch, mock_open, MagicMock
import json
import tempfile
import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestConfigLoader(unittest.TestCase):
    """Test cases for config_loader module."""

    def setUp(self):
        """Set up test fixtures."""
        self.test_config = {
            "table_mapping": {
                "schema.table1": {
                    "topic": "test-topic",
                    "group_id": "test-group",
                    "write_batch_size": 1000,
                    "batch_wait": 5.0
                }
            },
            "enable_discarded_messages_db": True,
            "enable_discarded_messages_file": False
        }

        self.test_schema = {
            "type": "record",
            "name": "TestRecord",
            "fields": [
                {"name": "id", "type": "int"},
                {"name": "name", "type": "string"}
            ]
        }

    def test_load_config_success(self):
        """Test successful configuration loading."""
        with patch('builtins.open', mock_open(read_data=json.dumps(self.test_config))):
            from config_loader import load_config

            result = load_config("test_config.json")

            self.assertEqual(result, self.test_config)

    def test_load_config_file_not_found(self):
        """Test handling of missing config file."""
        with patch('builtins.open', side_effect=FileNotFoundError("File not found")):
            with patch('config_loader.logging') as mock_logging:
                from config_loader import load_config

                result = load_config("missing_config.json")

                self.assertEqual(result, {})
                mock_logging.error.assert_called_once()

    def test_load_config_invalid_json(self):
        """Test handling of invalid JSON in config file."""
        with patch('builtins.open', mock_open(read_data="invalid json")):
            with patch('config_loader.logging') as mock_logging:
                from config_loader import load_config

                result = load_config("invalid_config.json")

                self.assertEqual(result, {})
                mock_logging.error.assert_called_once()

    def test_get_local_schema_success(self):
        """Test successful local schema loading."""
        with patch('builtins.open', mock_open(read_data=json.dumps(self.test_schema))):
            from config_loader import get_local_schema

            result = get_local_schema("test_schema.avsc")

            self.assertEqual(result, self.test_schema)

    def test_get_local_schema_file_not_found(self):
        """Test handling of missing schema file."""
        with patch('builtins.open', side_effect=FileNotFoundError("Schema file not found")):
            with patch('config_loader.logging') as mock_logging:
                from config_loader import get_local_schema

                result = get_local_schema("missing_schema.avsc")

                self.assertIsNone(result)
                mock_logging.error.assert_called_once()

    @patch('config_loader.get_kafka_credentials')
    @patch('config_loader.SchemaRegistryClient')
    def test_fetch_schema_from_registry_success(self, mock_registry_client, mock_get_credentials):
        """Test successful schema fetching from registry."""
        # Mock credentials
        mock_get_credentials.return_value = {"schema-registry": "http://test-registry:8081"}

        # Mock schema registry client
        mock_client = MagicMock()
        mock_registry_client.return_value = mock_client

        mock_schema = MagicMock()
        mock_schema.schema.schema_str = json.dumps(self.test_schema)
        mock_client.get_latest_version.return_value = mock_schema

        from config_loader import fetch_schema_from_registry

        result = fetch_schema_from_registry("test-topic")

        self.assertEqual(result, self.test_schema)
        mock_client.get_latest_version.assert_called_once_with("test-topic-value")

    @patch('config_loader.get_kafka_credentials')
    @patch('config_loader.SchemaRegistryClient')
    def test_fetch_schema_from_registry_failure(self, mock_registry_client, mock_get_credentials):
        """Test handling of schema registry failure."""
        mock_get_credentials.return_value = {"schema-registry": "http://test-registry:8081"}

        mock_client = MagicMock()
        mock_registry_client.return_value = mock_client
        mock_client.get_latest_version.side_effect = Exception("Registry error")

        with patch('config_loader.logging') as mock_logging:
            from config_loader import fetch_schema_from_registry

            result = fetch_schema_from_registry("test-topic")

            self.assertIsNone(result)
            mock_logging.error.assert_called_once()

    def test_get_config_value_table_priority(self):
        """Test that table config takes priority over global config."""
        from config_loader import get_config_value

        table_config = {"batch_size": 500}
        global_config = {"batch_size": 1000}

        result = get_config_value(table_config, global_config, "batch_size", 100)

        self.assertEqual(result, 500)

    def test_get_config_value_global_fallback(self):
        """Test fallback to global config when key not in table config."""
        from config_loader import get_config_value

        table_config = {"other_key": "value"}
        global_config = {"batch_size": 1000}

        result = get_config_value(table_config, global_config, "batch_size", 100)

        self.assertEqual(result, 1000)

    def test_get_config_value_default_fallback(self):
        """Test fallback to default when key not in either config."""
        from config_loader import get_config_value

        table_config = {"other_key": "value"}
        global_config = {"other_global_key": "value"}

        result = get_config_value(table_config, global_config, "batch_size", 100)

        self.assertEqual(result, 100)

    @patch('config_loader.psutil.process_iter')
    @patch('config_loader.setproctitle.setproctitle')
    def test_set_process_name(self, mock_setproctitle, mock_process_iter):
        """Test process name setting with instance numbering."""
        # Mock existing processes
        mock_process_iter.return_value = [
            MagicMock(info={'name': 'kafka_consumer_schema.table1_test-group_1'}),
            MagicMock(info={'name': 'other_process'})
        ]

        config = {
            "table_mapping": {
                "schema.table1": {
                    "group-id": "test-group",
                    "topic": "test-topic"
                }
            }
        }

        with patch('config_loader.logging'):
            from config_loader import set_process_name

            result = set_process_name("schema.table1", config)

            expected_name = "kafka_consumer_schema.table1_test-group_2"
            self.assertEqual(result, expected_name)
            mock_setproctitle.assert_called_once_with(expected_name)

    def test_get_discarded_messages_filename(self):
        """Test discarded messages filename generation."""
        from config_loader import get_discarded_messages_filename

        result = get_discarded_messages_filename("schema.table_name")

        expected = "discarded_messages/schema_table_name_discarded_messages.txt"
        self.assertEqual(result, expected)

    def test_get_discarded_messages_filename_special_chars(self):
        """Test filename generation with special characters."""
        from config_loader import get_discarded_messages_filename

        result = get_discarded_messages_filename("schema.table.with.dots")

        expected = "discarded_messages/schema_table_with_dots_discarded_messages.txt"
        self.assertEqual(result, expected)

    @patch('config_loader.os.makedirs')
    @patch('config_loader.logging.basicConfig')
    def test_setup_logging_default_params(self, mock_basic_config, mock_makedirs):
        """Test setup_logging with default parameters."""
        from config_loader import setup_logging

        setup_logging("test_process")

        mock_makedirs.assert_called_once_with("logs", exist_ok=True)
        mock_basic_config.assert_called_once()

        # Check that the call included the expected log filename
        call_args = mock_basic_config.call_args
        handlers = call_args[1]['handlers']
        self.assertEqual(len(handlers), 2)  # FileHandler and StreamHandler

    @patch('config_loader.os.makedirs')
    @patch('config_loader.logging.basicConfig')
    def test_setup_logging_custom_params(self, mock_basic_config, mock_makedirs):
        """Test setup_logging with custom parameters."""
        from config_loader import setup_logging

        setup_logging("custom_process", "DEBUG", "custom_logs")

        mock_makedirs.assert_called_once_with("custom_logs", exist_ok=True)
        mock_basic_config.assert_called_once()

        # Check that DEBUG level was set
        call_args = mock_basic_config.call_args
        self.assertEqual(call_args[1]['level'], 10)  # DEBUG level is 10


if __name__ == '__main__':
    unittest.main()
