# Test requirements for MSK Consumer refactored code
# These are the minimal requirements needed to run the unit tests

# Core testing framework (built into Python)
# unittest - included with Python standard library

# For more advanced testing features (optional)
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0

# For test coverage reporting (optional)
coverage>=7.0.0

# Note: The actual application dependencies are mocked in tests
# Production dependencies would include:
# - confluent-kafka
# - psycopg2-binary
# - fastavro
# - boto3
# - psutil
# - setproctitle
