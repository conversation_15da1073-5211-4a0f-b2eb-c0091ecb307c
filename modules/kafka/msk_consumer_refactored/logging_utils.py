import json
import logging
from datetime import datetime
from decimal import Decimal
from typing import Dict
from postgres_utils import db_connection

# Configuration constants - can be overridden by config
ENABLE_DISCARDED_MESSAGES_DB = True
ENABLE_DISCARDED_MESSAGES_FILE = True

class BytesEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, bytes):
            return obj.hex()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__str__'):
            return str(obj)
        return super().default(obj)

def make_json_serializable(obj):
    if isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [make_json_serializable(item) for item in obj]
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, bytes):
        return obj.hex()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    return obj

def log_discarded_message_db(message: Dict, reason: str, topic: str, offset: int) -> None:
    try:
        with db_connection() as conn:
            with conn.cursor() as cursor:
                query = """
                INSERT INTO ops.discarded_messages (message, error, topic, message_offset, created_at)
                VALUES (%s, %s, %s, %s, %s)
                """
                msg_json = json.dumps(make_json_serializable(message), cls=BytesEncoder)
                cursor.execute(query, (msg_json, reason, topic, offset, datetime.now()))
            conn.commit()
    except Exception as e:
        logging.error(f"Failed to log discarded message to DB: {e}")

def log_discarded_message_file(message: Dict, reason: str, topic: str, offset: int, file_path: str) -> None:
    try:
        with open(file_path, "a", encoding="utf-8") as f:
            log_line = f"{datetime.now()} - Topic: {topic} - Offset: {offset} - Reason: {reason} - Message: {json.dumps(make_json_serializable(message), cls=BytesEncoder)}\n"
            f.write(log_line)
    except Exception as e:
        logging.error(f"Failed to log discarded message to file: {e}")

def get_discarded_messages_filename(table_name: str) -> str:
    return f"{table_name.replace('.', '_')}_discarded_messages.txt"

def handle_discarded_message(message: Dict, reason: str, topic: str, offset: int, file_path: str) -> None:
    if ENABLE_DISCARDED_MESSAGES_DB:
        log_discarded_message_db(message, reason, topic, offset)
    if ENABLE_DISCARDED_MESSAGES_FILE:
        log_discarded_message_file(message, reason, topic, offset, file_path)