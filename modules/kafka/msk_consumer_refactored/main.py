import argparse
from kafka_utils import consume_messages
from config_loader import load_config

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Kafka Consumer with Schema Source Option")
    parser.add_argument("table_name", type=str, help="Target table name in the format schema.table")
    parser.add_argument("--use-local-schema", action="store_true", help="Load schema from local .avsc file instead of Schema Registry")
    parser.add_argument("--log-level", type=str, default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Set the logging level")
    args = parser.parse_args()

    config = load_config()
    # Add log level to config if provided via command line
    if args.log_level:
        config["log_level"] = args.log_level

    consume_messages(args.table_name, args.use_local_schema, config)