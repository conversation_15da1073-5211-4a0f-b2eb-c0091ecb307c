import json
import logging
import psutil
import setproctitle
import os
from typing import Dict, Any, Optional

class ConsumerConfig:
    def __init__(self, config_dict: dict):
        self.table_mapping = config_dict.get("table_mapping", {})
        self.enable_discarded_messages_db = config_dict.get("enable_discarded_messages_db", True)
        self.enable_discarded_messages_file = config_dict.get("enable_discarded_messages_file", False)
        # Add other config properties as needed

    def get_table_config(self, table_name: str) -> Dict:
        return self.table_mapping.get(table_name, {})

    def get_batch_size(self, table_name: str) -> int:
        table_config = self.get_table_config(table_name)
        return table_config.get("write_batch_size", 500)

    def get_batch_wait_time(self, table_name: str) -> float:
        table_config = self.get_table_config(table_name)
        return table_config.get("batch_wait", 2.0)

def load_config(path: str = "./config.json") -> Dict:
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Failed to load config: {e}")
        return {}

def setup_logging(process_name: str, log_level: str = "INFO", log_dir: str = "logs") -> None:
    """
    Configure logging to use a file named after the process name.

    Args:
        process_name: The name of the process (used for log filename)
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_dir: Directory to store log files
    """
    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)

    # Create log filename based on process name
    log_filename = f"{log_dir}/{process_name}.log"

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()  # Also log to console
        ]
    )

    logging.info(f"Logging configured for process '{process_name}' -> {log_filename}")

def set_process_name(table_name: str, config: Dict) -> str:
    table_config = config["table_mapping"][table_name]
    group_id = table_config.get("group-id", "data-platform")
    base_name = f"kafka_consumer_{table_name}_{group_id}"
    instance_num = sum(
        1 for proc in psutil.process_iter(['name'])
        if proc.info['name'] and proc.info['name'].startswith(base_name)
    ) + 1
    process_name = f"{base_name}_{instance_num}"
    setproctitle.setproctitle(process_name)
    return process_name