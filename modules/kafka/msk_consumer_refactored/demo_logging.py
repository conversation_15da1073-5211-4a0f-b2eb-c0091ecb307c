#!/usr/bin/env python3
"""
Demonstration script showing how the logging system works with process names.
This script simulates the logging behavior without requiring Kafka/PostgreSQL.
"""

import sys
import os
from unittest.mock import MagicMock

# Mock external dependencies
sys.modules['confluent_kafka'] = MagicMock()
sys.modules['confluent_kafka.schema_registry'] = MagicMock()
sys.modules['psycopg2'] = MagicMock()
sys.modules['fastavro'] = MagicMock()
sys.modules['psutil'] = MagicMock()
sys.modules['setproctitle'] = MagicMock()
sys.modules['boto3'] = MagicMock()
sys.modules['botocore'] = MagicMock()
sys.modules['botocore.exceptions'] = MagicMock()

# Mock psutil to simulate existing processes
mock_psutil = MagicMock()
mock_process1 = MagicMock()
mock_process1.info = {'name': 'kafka_consumer_schema.table1_test-group_1'}
mock_process2 = MagicMock()
mock_process2.info = {'name': 'other_process'}
mock_psutil.process_iter.return_value = [mock_process1, mock_process2]
sys.modules['psutil'] = mock_psutil

# Mock setproctitle
mock_setproctitle = MagicMock()
sys.modules['setproctitle'] = mock_setproctitle

from config_loader import set_process_name, setup_logging

def demo_logging_system():
    """Demonstrate the logging system with different scenarios."""

    print("🔧 MSK Consumer Logging System Demo")
    print("=" * 50)

    # Test configuration
    test_config = {
        "table_mapping": {
            "schema.table1": {
                "topic": "test-topic",
                "group-id": "test-group"
            },
            "analytics.user_events": {
                "topic": "user-events-topic",
                "group-id": "analytics-group"
            }
        }
    }

    # Demo 1: First consumer instance
    print("\n📝 Demo 1: First consumer instance for schema.table1")
    process_name1 = set_process_name("schema.table1", test_config)
    print(f"   Process name: {process_name1}")

    setup_logging(process_name1, "INFO")
    expected_log_file1 = f"logs/{process_name1}.log"
    print(f"   Log file: {expected_log_file1}")

    # Demo 2: Second consumer instance (simulating another instance)
    print("\n📝 Demo 2: Second consumer instance for schema.table1")
    # Mock an additional process for the second instance
    mock_process3 = MagicMock()
    mock_process3.info = {'name': f'{process_name1}'}
    mock_psutil.process_iter.return_value = [mock_process1, mock_process2, mock_process3]

    process_name2 = set_process_name("schema.table1", test_config)
    print(f"   Process name: {process_name2}")

    setup_logging(process_name2, "DEBUG")
    expected_log_file2 = f"logs/{process_name2}.log"
    print(f"   Log file: {expected_log_file2}")

    # Demo 3: Different table
    print("\n📝 Demo 3: Consumer for different table")
    process_name3 = set_process_name("analytics.user_events", test_config)
    print(f"   Process name: {process_name3}")

    setup_logging(process_name3, "WARNING", "custom_logs")
    expected_log_file3 = f"custom_logs/{process_name3}.log"
    print(f"   Log file: {expected_log_file3}")

    # Demo 4: Show log file naming pattern
    print("\n📋 Log File Naming Pattern:")
    print("   Format: logs/{process_name}.log")
    print("   Where process_name = kafka_consumer_{table_name}_{group_id}_{instance_num}")
    print("\n   Examples:")
    print(f"   - {expected_log_file1}")
    print(f"   - {expected_log_file2}")
    print(f"   - {expected_log_file3}")

    # Demo 5: Command line usage
    print("\n🚀 Command Line Usage:")
    print("   python3 main.py schema.table1")
    print("   python3 main.py schema.table1 --log-level DEBUG")
    print("   python3 main.py analytics.user_events --use-local-schema --log-level WARNING")

    print("\n✅ Demo complete! Each consumer instance gets its own log file based on the process name.")

if __name__ == "__main__":
    demo_logging_system()
