# MSK Consumer - Comprehensive Documentation

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture & File Structure](#architecture--file-structure)
3. [Core Functionality](#core-functionality)
4. [Testing Framework](#testing-framework)
5. [Usage & Configuration](#usage--configuration)
6. [Development Guidelines](#development-guidelines)

---

## 🎯 Project Overview

This is a comprehensive refactored MSK (Managed Streaming for Kafka) Consumer application that processes Avro messages from Kafka topics and inserts them into PostgreSQL databases. The application has been transformed from a monolithic script into a modular, maintainable, and thoroughly tested codebase.

### Key Features
- ✓ **Modular Architecture**: Clean separation of concerns across multiple modules
- ✓ **Dynamic Logging**: Process-specific log files with configurable levels
- ✓ **Comprehensive Testing**: Complete test coverage with proper mocking
- ✓ **Robust Error Handling**: DLQ support and comprehensive exception management
- ✓ **Batch Processing**: Efficient batch insertion with individual record fallback
- ✓ **Schema Management**: Support for both local schemas and schema registry
- ✓ **Connection Pooling**: Efficient database connection management
- ✓ **AWS Integration**: Secrets Manager for credential management

### Core Capabilities
- **Avro Message Processing**: Decodes Avro messages with decimal/date handling
- **Batch Processing**: Configurable batch size and wait time for optimal performance
- **Dead Letter Queue (DLQ)**: Automatic topic creation and failed message handling
- **Database Operations**: Connection pooling with retry logic and metadata caching
- **Schema Registry Integration**: Support for both local schema files and registry
- **Process Management**: Unique process naming and monitoring integration
- **Discarded Message Logging**: Database and file-based logging for failed messages
- **PostgreSQL Integration**: Value adaptation and type conversion for PostgreSQL

---

## 🏛️ Architecture & File Structure

### File Structure

```
msk_consumer_refactored/
├── main.py                    # Entry point and argument parsing
├── kafka_utils.py            # Kafka consumer logic and DLQ management
├── postgres_utils.py         # Database operations and schema processing
├── postgres_batch_utils.py   # Batch processing and data adaptation
├── config_loader.py          # Configuration and schema loading
├── logging_utils.py          # Logging and discarded message handling
├── secrets_manager.py        # AWS Secrets Manager integration
├── get_secrets.py            # AWS Secrets Manager client
├── discarded_messages/       # Directory for discarded message files
├── logs/                     # Directory for log files
├── custom_logs/              # Directory for custom log files
└── tests/                    # Comprehensive test suite
    ├── __init__.py
    ├── README.md
    ├── requirements-test.txt
    ├── run_tests.py
    ├── test_get_secrets.py
    ├── test_secrets_manager.py
    ├── test_config_loader.py
    ├── test_postgres_utils.py
    ├── test_postgres_batch_utils.py
    ├── test_kafka_utils.py
    ├── test_logging_utils.py
    └── test_main.py
```

### Architecture Cleanup

**Legacy Files Removed:**
- ✅ **`db.py`**: Redundant database manager class (functionality moved to `postgres_utils.py`)
- ✅ **`config.py`**: Redundant configuration classes (functionality moved to `config_loader.py`)

**Benefits:**
- **Eliminated Duplication**: Removed redundant database connection and configuration logic
- **Single Source of Truth**: All database operations now in `postgres_utils.py`
- **Cleaner Architecture**: Reduced codebase complexity and maintenance overhead
- **Consistent Patterns**: Unified approach to database and configuration management

### Module Responsibilities

#### **main.py**
- Entry point for the application
- Command-line argument parsing
- Integration with config loading and consumer initialization

#### **kafka_utils.py**
- Kafka consumer configuration and management
- Message polling and processing coordination
- Dead Letter Queue (DLQ) topic management
- Schema loading (local files vs. registry)
- Consumer lifecycle management

#### **postgres_utils.py**
- Database connection pool management
- Table metadata retrieval and caching (columns, primary keys, types)
- Decimal type processing and conversion
- Date/timestamp field conversion (schema-based and column type-based)
- Avro message processing pipeline
- Database operation retry logic

#### **postgres_batch_utils.py**
- Basic data adaptation for PostgreSQL (non-processing types only)
- Batch insertion with fallback to individual records
- Error handling and DLQ integration
- Discarded message logging
- PostgreSQL conflict resolution (ON CONFLICT handling)

#### **config_loader.py**
- Configuration file loading and validation
- Schema management (local and registry)
- Process name generation with instance numbering
- Logging setup and configuration
- Discarded messages filename generation
- Global configuration flags (ENABLE_DLQ, ENABLE_DISCARDED_MESSAGES_*)

#### **logging_utils.py**
- Custom JSON encoding for complex types
- Database logging to ops.discarded_messages table
- File-based logging for discarded messages
- Dual logging strategy for maximum reliability
- Error handling for logging failures
- Configuration-based logging enablement

#### **secrets_manager.py**
- Credential caching functionality
- Independent caching for Kafka and Postgres credentials
- First call vs. cached call behavior
- Error handling when secret retrieval fails

#### **get_secrets.py**
- AWS Secrets Manager client integration
- Secret retrieval with error handling
- JSON parsing and validation
- Regional configuration support

### Dependencies

#### **Core Dependencies:**
- `confluent-kafka` - Kafka client library
- `psycopg2` - PostgreSQL database adapter
- `fastavro` - Fast Avro serialization library
- `boto3` - AWS SDK for Python
- `psutil` - System and process utilities
- `setproctitle` - Process title setting

#### **Test Dependencies:**
- `unittest` - Python's built-in testing framework
- `unittest.mock` - Mocking framework for tests

### Data Flow Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Kafka Topic   │───▶│  MSK Consumer    │───▶│   PostgreSQL    │
│                 │    │                  │    │ Target Database │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  Dead Letter     │
                       │  Queue (DLQ)     │
                       └──────────────────┘
                              │
                              ▼
                    ┌─────────────────────────┐
                    │     Error Logging       │
                    │                         │
                    │  ┌─────────────────┐    │
                    │  │ ops.discarded_  │    │
                    │  │   messages      │    │
                    │  │ (PostgreSQL)    │    │
                    │  └─────────────────┘    │
                    │           +             │
                    │  ┌─────────────────┐    │
                    │  │  File-based     │    │
                    │  │    Logging      │    │
                    │  │ (discarded_     │    │
                    │  │  messages/)     │    │
                    │  └─────────────────┘    │
                    └─────────────────────────┘
```

#### **Processing Pipeline with Comprehensive Error Handling:**
1. **Message Consumption**: Kafka consumer polls messages from configured topics
2. **Avro Processing**: Schema parsing, validation, and Avro decoding with error logging
3. **Data Conversion**: Decimal and timestamp field conversion with stage-specific error handling
4. **Batch Processing**: Messages are batched for efficient database insertion
5. **Database Insertion**: Batch insert with individual record fallback on errors
6. **Multi-Stage Error Handling**: Failed messages logged at each processing stage
7. **DLQ Integration**: Failed messages sent to Dead Letter Queue topics
8. **Dual Logging**: Errors logged to both PostgreSQL ops.discarded_messages table and local files
9. **Monitoring**: Process-specific logging and comprehensive error tracking

#### **Error Handling Stages:**
- **Stage 1**: Avro schema parsing and validation errors
- **Stage 2**: Avro message decoding errors
- **Stage 3**: Decimal field conversion errors
- **Stage 4**: Date/timestamp field conversion errors
- **Stage 5**: Database value adaptation errors
- **Stage 6**: Batch insertion errors with individual fallback
- **Stage 7**: Critical processing errors with comprehensive logging

---

## ⚙️ Core Functionality

### Dynamic Logging Implementation

#### **Process-Specific Log Files**
The log file name is automatically determined based on the process name:

```
Log File Format: logs/{process_name}.log
Process Name Format: kafka_consumer_{table_name}_{group_id}_{instance_num}
```

#### **Process Name Generation**
Each consumer instance gets a unique process name:

```python
def set_process_name(table_name: str, config: Dict) -> str:
    table_config = config["table_mapping"][table_name]
    group_id = table_config.get("group-id", "data-platform")
    base_name = f"kafka_consumer_{table_name}_{group_id}"
    instance_num = sum(
        1 for proc in psutil.process_iter(['name'])
        if proc.info['name'] and proc.info['name'].startswith(base_name)
    ) + 1
    process_name = f"{base_name}_{instance_num}"
    setproctitle.setproctitle(process_name)
    return process_name
```

#### **Logging Configuration**
Automatic logging setup with dual output (file and console):

```python
def setup_logging(process_name: str, log_level: str = "INFO", log_dir: str = "logs") -> None:
    os.makedirs(log_dir, exist_ok=True)
    log_filename = f"{log_dir}/{process_name}.log"
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )
```

### Message Processing Pipeline

#### **Comprehensive Error Handling at Every Stage**

**Error Handling Implementation Summary:**

| Stage | Location | Error Types | Context Preserved | DLQ Integration | DB Logging |
|-------|----------|-------------|-------------------|-----------------|------------|
| **1. Message Consumption** | kafka_utils.py | Polling, validation | Raw message | ✅ | ✅ |
| **2. Schema Processing** | postgres_utils.py | Parsing, validation | Raw + schema | ✅ | ✅ |
| **3. Avro Decoding** | postgres_utils.py | Binary format, compatibility | Raw + parsed schema | ✅ | ✅ |
| **4. Decimal Conversion** | postgres_utils.py | Decimal format, scale | Record + decimal fields | ✅ | ✅ |
| **5. Timestamp Conversion** | postgres_utils.py | Date/time format, logical types | Record + column types | ✅ | ✅ |
| **6. Value Adaptation** | postgres_batch_utils.py | Type compatibility, JSON | Record + table metadata | ✅ | ✅ |
| **7. Batch Insertion** | postgres_batch_utils.py | DB conflicts, connectivity | Batch + operation details | ✅ | ✅ |
| **8. Individual Fallback** | postgres_batch_utils.py | Final processing failures | Complete processing history | ✅ | ✅ |

**Key Features:**
- **Complete Coverage**: Error handling at every processing stage
- **Context Preservation**: Rich error context for debugging
- **Dual Recovery**: Both DLQ and database logging
- **Progressive Fallback**: Batch → Individual → Error logging
- **Audit Trail**: Complete processing history for failed messages

#### **Avro Message Decoding**
- Decimal type processing with variable scale support and error handling
- Date/timestamp field conversion with comprehensive error logging
- Complex nested data structure handling with stage-specific errors
- Multi-stage schema validation and error context preservation

#### **Batch Processing**
- Configurable batch size and wait time
- Efficient batch insertion with PostgreSQL COPY
- Individual record fallback on batch failures
- Transaction management and rollback support

#### **Error Handling & DLQ**
- Dead Letter Queue topic creation and management
- Failed message routing to DLQ topics
- Comprehensive error logging and monitoring
- Retry logic with exponential backoff

#### **Database Operations**
- Connection pooling for optimal performance
- Table metadata caching (columns, primary keys, types)
- PostgreSQL value adaptation and type conversion
- Prepared statement optimization
- Batch insert with conflict resolution
- Individual record fallback on batch failures

### Schema Management

#### **Local Schema Files**
- JSON schema file loading and parsing
- Decimal field identification and processing
- Schema validation and error handling

#### **Schema Registry Integration**
- Remote schema fetching with caching
- Version management and compatibility
- Fallback to local schemas on registry failures

### Error Logging & Monitoring

#### **PostgreSQL ops.discarded_messages Table**
The system maintains a comprehensive audit trail of all failed message processing in a dedicated PostgreSQL table:

```sql
-- Table structure for error tracking
CREATE TABLE ops.discarded_messages (
    id SERIAL PRIMARY KEY,
    message JSONB,              -- Full message content in JSON format
    error TEXT,                 -- Error description and reason for failure
    topic VARCHAR(255),         -- Source Kafka topic
    message_offset BIGINT,      -- Kafka message offset
    created_at TIMESTAMP        -- When the error was logged
);
```

**Key Features:**
- **Complete Message Preservation**: Full message content stored as JSONB for analysis
- **Error Context**: Detailed error descriptions including stack traces
- **Kafka Metadata**: Topic and offset information for message traceability
- **Temporal Tracking**: Timestamp for error occurrence analysis
- **Queryable Format**: JSONB allows complex queries on message content

#### **Dual Logging Strategy**
The system implements redundant error logging for maximum reliability:

**1. Database Logging (Primary)**
```python
INSERT INTO ops.discarded_messages (message, error, topic, message_offset, created_at)
VALUES (%s, %s, %s, %s, %s)
```
- **Structured Storage**: Enables SQL queries and reporting
- **Integration Ready**: Easy integration with monitoring dashboards
- **Persistent**: Survives application restarts and deployments

**2. File-based Logging (Backup)**
```
discarded_messages/
├── schema_table1_discarded_messages.txt
├── analytics_events_discarded_messages.txt
└── orders_transactions_discarded_messages.txt
```
- **Fallback Mechanism**: Works even if database is unavailable
- **Table-specific Files**: Organized by table for easier analysis
- **Human Readable**: Direct file access for debugging

### Database Insert Operations

The MSK Consumer implements a sophisticated insert strategy with conflict resolution and fallback mechanisms to ensure data reliability and performance.

#### **Insert Flow Architecture**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Batch of      │───▶│  Primary Key     │───▶│   Batch Insert  │
│   Messages      │    │  Detection       │    │  w/ ON CONFLICT │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                                ┌─────────────────┐
                                                │ Batch Success?  │
                                                │ (conflicts      │
                                                │  resolved)      │
                                                └─────────────────┘
                                                    │           │
                                                   Yes          No
                                                    │           │
                                                    ▼           ▼
                                            ┌─────────────┐ ┌─────────────────┐
                                            │   Commit    │ │  Individual     │
                                            │ Transaction │ │  Record Insert  │
                                            └─────────────┘ │ w/ ON CONFLICT  │
                                                            └─────────────────┘
                                                                    │
                                                                    ▼
                                                            ┌─────────────────┐
                                                            │ Record Success? │
                                                            │ (conflicts      │
                                                            │  resolved)      │
                                                            └─────────────────┘
                                                                │       │
                                                            Success   Failure
                                                                │       │
                                                                ▼       ▼
                                                        ┌─────────────┐ ┌─────────────┐
                                                        │   Record    │ │   Send to   │
                                                        │  Processed  │ │     DLQ     │
                                                        └─────────────┘ └─────────────┘
```

#### **Dual-Level Insert Strategy**

**1. Primary Batch Insert (with op_ts-based ON CONFLICT)**
```sql
INSERT INTO target_table (col1, col2, col3, op_ts, ...)
VALUES
    (val1_1, val1_2, val1_3, timestamp1, ...),
    (val2_1, val2_2, val2_3, timestamp2, ...),
    (val3_1, val3_2, val3_3, timestamp3, ...)
ON CONFLICT (primary_key_columns) DO UPDATE SET
    col1 = EXCLUDED.col1,
    col2 = EXCLUDED.col2,
    col3 = EXCLUDED.col3,
    op_ts = EXCLUDED.op_ts
WHERE target_table.op_ts < EXCLUDED.op_ts
```
- **Timestamp-Protected Updates**: Only updates if incoming record is newer
- **Prevents Data Regression**: Older messages cannot overwrite newer data
- **Batch Performance**: Processes entire batch with temporal conflict resolution
- **Event Stream Consistency**: Maintains proper ordering in event-driven systems

**2. Individual Record Fallback (same op_ts-based ON CONFLICT)**
When batch insert fails due to non-conflict errors, each record is processed individually:
```sql
INSERT INTO target_table (col1, col2, col3, op_ts, ...)
VALUES (val1, val2, val3, timestamp, ...)
ON CONFLICT (primary_key_columns) DO UPDATE SET
    col1 = EXCLUDED.col1,
    col2 = EXCLUDED.col2,
    col3 = EXCLUDED.col3,
    op_ts = EXCLUDED.op_ts
WHERE target_table.op_ts < EXCLUDED.op_ts
```
- **Same Temporal Logic**: Identical timestamp-based conflict resolution
- **Granular Processing**: Isolates problematic records while maintaining consistency
- **Fallback Safety**: Ensures same data integrity rules apply at individual record level

#### **Conflict Resolution Strategies**

**1. Tables WITH Primary Keys AND op_ts Column (Timestamp-Based)**
- Uses `ON CONFLICT (primary_key_columns) DO UPDATE SET ... WHERE table.op_ts < EXCLUDED.op_ts`
- **Timestamp Protection**: Only updates if incoming record is newer than existing record
- **Prevents Data Regression**: Older messages cannot overwrite newer data
- **Optimal for Event Sourcing**: Maintains temporal consistency in data streams

**2. Tables WITH Primary Keys WITHOUT op_ts Column (Standard)**
- Uses `ON CONFLICT (primary_key_columns) DO UPDATE SET ...`
- **Always Updates**: Incoming records always overwrite existing records
- **Simple Conflict Resolution**: No timestamp comparison
- **Suitable for**: Tables where latest message is always desired

**3. Tables WITHOUT Primary Keys (Successful Query, Empty Result)**
- Uses basic `INSERT` without conflict resolution
- **No Conflict Handling**: Relies on database constraints for duplicate prevention
- **Performance Optimized**: No overhead from conflict checking
- **Suitable for**: Append-only tables or tables with application-level deduplication

**4. Database Connectivity Issues (Query Failures)**
- **Retry Logic**: Up to 5 attempts with exponential backoff (2s, 4s, 8s, 16s, 32s)
- **Failure Propagation**: Batch fails rather than using incorrect conflict strategy
- **Error Types Handled**: Connection timeouts, permission errors, network issues
- **Data Integrity**: Prevents silent data corruption from wrong conflict resolution

#### **Insert Operation Details**

**Batch Processing Flow:**
1. **Metadata Retrieval with Retry**: Get table columns and primary key information (with exponential backoff)
2. **Data Preparation**: Convert and adapt values for PostgreSQL
3. **Batch Assembly**: Group messages into configurable batch sizes
4. **Primary Key Validation**: Distinguish between "no primary keys" vs "can't fetch primary keys"
5. **Batch Insert with Conflict Resolution**: Attempt batch insert with appropriate ON CONFLICT handling
6. **Batch Success Check**: Verify batch completed (conflicts resolved automatically)
7. **Individual Fallback**: If batch fails (non-conflict errors), process records individually
8. **Individual Conflict Resolution**: Each record gets same ON CONFLICT treatment with retry logic
9. **Final Error Handling**: Route truly failed records to DLQ and logging

**Enhanced Error Handling:**
- **Primary Key Fetch Retry**: Up to 5 attempts with exponential backoff for database connectivity issues
- **Error Type Distinction**: Differentiates between "table has no PKs" vs "can't access database"
- **Graceful Degradation**: Only falls back to basic INSERT when query succeeds but returns empty
- **Consistent Retry Logic**: Same retry behavior for both batch and individual processing
- **Failure Propagation**: Database connectivity issues cause batch failure rather than silent degradation

**Why Dual-Level ON CONFLICT?**
- **Batch Level**: Efficiently handles conflicts for entire batches (primary strategy)
- **Individual Level**: Ensures conflict resolution even when batch processing fails
- **Fault Isolation**: Separates conflict resolution from other types of errors
- **Data Consistency**: Guarantees same conflict behavior regardless of processing path
- **Robust Metadata Access**: Persistent retry logic ensures proper conflict resolution strategy

**Value Adaptation:**
- **Decimal Types**: Convert Avro decimals to PostgreSQL NUMERIC
- **Date/Time Fields**: Handle timezone conversion and formatting using schema logical types
- **Timestamp Conversion**: Generic conversion of integer timestamps to PostgreSQL timestamp format based on column types
- **JSON Fields**: Serialize complex objects to JSON strings
- **NULL Handling**: Proper NULL value processing
- **Type Coercion**: Automatic type conversion where safe

#### **Performance Optimizations**

**Connection Pooling:**
```python
# Efficient connection management
connection_pool = psycopg2.pool.ThreadedConnectionPool(
    minconn=1,
    maxconn=20,
    host=db_config['host'],
    database=db_config['database'],
    user=db_config['user'],
    password=db_config['password']
)
```

**Prepared Statements:**
- Dynamic SQL generation based on table structure
- Parameterized queries to prevent SQL injection
- Optimized execution plans for repeated operations

**Batch Size Configuration:**
```json
{
  "table_mapping": {
    "schema.table1": {
      "batch_size": 1000,        // Records per batch
      "batch_wait_time": 30      // Seconds to wait for batch completion
    }
  }
}
```

#### **Error Handling & Recovery**

**Transaction Management:**
- Each batch operation runs in a transaction
- Automatic rollback on batch failures
- Individual record transactions for fallback processing

**Retry Logic:**
- Configurable retry attempts for transient failures
- Exponential backoff for connection issues
- Circuit breaker pattern for persistent failures

**Dead Letter Queue Integration:**
- Failed records automatically sent to DLQ topics
- Preserves original message structure and metadata
- Enables manual review and reprocessing

---

## 🔬 Testing Framework

### Overview

The test suite provides complete coverage of all modules using proper mocking to isolate functionality and avoid dependencies on external services.

### Test Structure

```
tests/
├── __init__.py                    # Test package initialization
├── README.md                      # Test documentation
├── requirements-test.txt          # Test dependencies
├── run_tests.py                   # Test runner with mocking setup
├── test_get_secrets.py           # Tests for AWS Secrets Manager integration
├── test_secrets_manager.py       # Tests for credential caching
├── test_config_loader.py         # Tests for configuration management
├── test_postgres_utils.py        # Tests for database operations
├── test_postgres_batch_utils.py  # Tests for batch processing
├── test_kafka_utils.py           # Tests for Kafka functionality
├── test_logging_utils.py         # Tests for logging utilities
└── test_main.py                  # Tests for main entry point
```

### Running Tests

#### **Option 1: Using the Test Runner (Recommended)**

```bash
cd modules/kafka/msk_consumer_refactored/tests
python3 run_tests.py
```

This automatically sets up all necessary mocks and runs all tests.

#### **Option 2: Using unittest directly**

```bash
cd modules/kafka/msk_consumer_refactored
python3 -m unittest discover tests -v
```

#### **Option 3: Running individual test files**

```bash
cd modules/kafka/msk_consumer_refactored
python3 -m unittest tests.test_postgres_utils -v
```

### Test Coverage

#### **test_get_secrets.py**
- ✓ Successful secret retrieval
- ✓ Error handling for missing secrets
- ✓ Invalid JSON handling
- ✓ ClientError exception handling
- ✓ Default and custom region parameters

#### **test_secrets_manager.py**
- ✓ Credential caching functionality
- ✓ Independent caching for Kafka and Postgres credentials
- ✓ First call vs. cached call behavior
- ✓ Error handling when secret retrieval fails

#### **test_config_loader.py**
- ✓ Configuration file loading (success and failure cases)
- ✓ Local schema file loading
- ✓ Schema registry integration
- ✓ Configuration value resolution with priority
- ✓ Process name generation with instance numbering
- ✓ Discarded messages filename generation

#### **test_postgres_utils.py**
- ✓ Database connection pool management
- ✓ Table column and primary key retrieval with caching
- ✓ Decimal type processing and conversion
- ✓ Date/timestamp field conversion
- ✓ Avro message processing pipeline
- ✓ Retry logic for database operations

#### **test_postgres_batch_utils.py**
- ✓ Data serialization and adaptation for PostgreSQL
- ✓ Batch insertion with fallback to individual records
- ✓ Error handling and DLQ integration
- ✓ Discarded message logging
- ✓ Variable scale decimal processing

#### **test_kafka_utils.py**
- ✓ DLQ topic management and message sending
- ✓ Consumer configuration and message polling
- ✓ Schema loading (local files vs. registry)
- ✓ Message processing pipeline integration
- ✓ Error handling and logging

#### **test_logging_utils.py**
- ✓ Custom JSON encoding for complex types
- ✓ Database logging to ops.discarded_messages table
- ✓ File-based logging for discarded messages
- ✓ Dual logging strategy validation
- ✓ Error handling for logging failures
- ✓ Configuration-based logging enablement

#### **test_main.py**
- ✓ Command-line argument parsing
- ✓ Integration with config loading and consumer
- ✓ Boolean flag handling
- ✓ Error handling for missing arguments

### Mocking Strategy

The tests use comprehensive mocking to isolate functionality:

#### **External Dependencies Mocked:**
- **AWS Services**: `boto3`, `botocore.exceptions`
- **Kafka**: `confluent_kafka`, `confluent_kafka.admin`, `confluent_kafka.schema_registry`
- **Database**: `psycopg2`, `psycopg2.pool`, `psycopg2.extras`
- **Data Processing**: `fastavro`
- **System**: `psutil`, `setproctitle`

#### **Mock Data Provided:**
- Realistic AWS Secrets Manager responses
- Sample Avro schemas and messages
- Database connection and cursor behaviors
- Kafka consumer and producer interactions

### Test Features

#### **Comprehensive Error Testing**
- Network failures and timeouts
- Invalid data formats
- Missing configuration files
- Database connection errors
- Schema registry failures

#### **Edge Case Coverage**
- Empty batches and null values
- Large decimal numbers and dates
- Complex nested data structures
- Concurrent process naming
- Cache invalidation scenarios

#### **Integration Testing**
- End-to-end message processing pipeline
- Configuration loading and validation
- Error propagation and handling
- Logging and monitoring integration

### Best Practices

#### **Test Isolation**
- Each test is independent and can run in any order
- Mocks are reset between tests
- No shared state between test cases

#### **Realistic Scenarios**
- Tests use realistic data formats and sizes
- Error conditions mirror real-world failures
- Configuration matches production patterns

#### **Maintainability**
- Clear test names describing the scenario
- Comprehensive docstrings explaining test purpose
- Modular setup and teardown methods

### Adding New Tests

When adding new functionality:

1. **Create test file**: Follow naming pattern `test_<module_name>.py`
2. **Add test class**: Inherit from `unittest.TestCase`
3. **Mock dependencies**: Use `@patch` decorators for external calls
4. **Test scenarios**: Cover success, failure, and edge cases
5. **Update documentation**: Add test coverage to this README

### Troubleshooting

#### **Common Issues**

**Import Errors**: Ensure you're running tests from the correct directory and that the parent directory is in the Python path.

**Mock Failures**: Check that all external dependencies are properly mocked in the test setup.

**Test Isolation**: If tests fail when run together but pass individually, check for shared state or incomplete mocking.

#### **Debug Mode**

For detailed debugging, run tests with:

```bash
python3 -m unittest tests.test_module_name.TestClass.test_method -v
```

---

## 📖 Usage & Configuration

### Command Line Interface

The refactored code maintains the same CLI interface as the original:

```bash
python3 main.py <table_name> [--use-local-schema] [--log-level <level>]
```

#### **Parameters:**
- **`table_name`** (required): The table name from the configuration mapping
- **`--use-local-schema`** (optional): Use local schema files instead of schema registry
- **`--log-level`** (optional): Set logging level (DEBUG, INFO, WARNING, ERROR)

#### **Usage Examples:**

```bash
# Basic usage with schema registry
python3 main.py schema.table1

# Use local schema files
python3 main.py schema.table1 --use-local-schema

# Set custom log level
python3 main.py schema.table1 --log-level DEBUG

# Combined options
python3 main.py analytics.events --use-local-schema --log-level WARNING
```

### Configuration Management

#### **Global Configuration Flags:**
The system uses centralized configuration flags defined in `config_loader.py`:

```python
# Global configuration flags in config_loader.py
ENABLE_DLQ = True                      # Enable Dead Letter Queue functionality
ENABLE_DISCARDED_MESSAGES_DB = True   # Log failed messages to PostgreSQL ops table
ENABLE_DISCARDED_MESSAGES_FILE = False # Log failed messages to local files
DLQ_SUFFIX = "-DLQ"                   # Suffix for DLQ topic names
STRICT_FIELD_VALIDATION = False       # Fail processing on field mismatches
ENABLE_TIMESTAMP_CONVERSION = True    # Attempt automatic timestamp conversion
```

**Benefits of Centralized Configuration:**
- **Single Source of Truth**: All configuration flags in one location
- **No Duplication**: Eliminates scattered configuration constants
- **Easy Maintenance**: Change behavior by modifying one file
- **Consistent Behavior**: Same configuration used across all modules

#### **Field Validation Behavior:**

The system includes comprehensive field validation that compares incoming message fields with target table columns:

**Default Behavior (STRICT_FIELD_VALIDATION = False):**
- **Extra Fields**: Logged as warnings, then ignored (not inserted)
- **Missing Fields**: Logged as warnings, then set to NULL in database
- **Processing**: Continues despite field mismatches
- **Use Case**: Flexible processing where schema evolution is expected

**Strict Mode (STRICT_FIELD_VALIDATION = True):**
- **Extra Fields**: Cause processing failure and DLQ routing
- **Missing Fields**: Cause processing failure and DLQ routing
- **Processing**: Stops immediately on field mismatches
- **Use Case**: Strict schema enforcement where exact field matching is required

**Example Scenarios:**

| Source Fields | Target Columns | Default Mode | Strict Mode |
|---------------|----------------|--------------|-------------|
| `{id, name, value}` | `[id, name, value]` | ✅ Success | ✅ Success |
| `{id, name, value, extra}` | `[id, name, value]` | ⚠️ Warning + Process | ❌ Fail + DLQ |
| `{id, name}` | `[id, name, value]` | ⚠️ Warning + NULL | ❌ Fail + DLQ |
| `{id, extra1, extra2}` | `[id, name, value]` | ⚠️ Warning + NULLs | ❌ Fail + DLQ |

**Validation Logging:**
```
Field validation for schema.table: 2 matching, 1 extra, 1 missing
Extra fields in source data for schema.table will be ignored: ['extra_field']
Missing fields in source data for schema.table will be set to NULL: ['missing_field']
```

#### **Robust Timestamp Conversion:**

The system includes intelligent timestamp conversion with bounds checking to prevent errors like "year 57414 is out of range":

**Timestamp Detection Logic:**
- **Milliseconds**: Values > 1,000,000,000,000 (> year 2001 in milliseconds)
- **Seconds**: Values between 946,684,800 and 253,402,300,799 (years 2000-9999)
- **Out of Range**: Values outside reasonable timestamp bounds are kept as-is

**Bounds Checking:**
```python
# Maximum valid timestamps
MAX_TIMESTAMP_SECONDS = 253402300799      # 9999-12-31 23:59:59
MAX_TIMESTAMP_MILLIS = 253402300799999    # 9999-12-31 23:59:59.999
MAX_TIMESTAMP_MICROS = 253402300799999999 # 9999-12-31 23:59:59.999999

# Minimum reasonable timestamp
MIN_TIMESTAMP_SECONDS = 946684800         # 2000-01-01 00:00:00
```

**Error Handling:**
- **Out of Range Values**: Logged as warnings, original value preserved
- **Invalid Formats**: Graceful fallback, no processing failures
- **Configurable**: Can be disabled via `ENABLE_TIMESTAMP_CONVERSION = False`

**Example Scenarios:**
| Value | Interpretation | Action | Result |
|-------|----------------|--------|---------|
| `1640995200` | Seconds (2022-01-01) | ✅ Convert | `"2022-01-01 00:00:00"` |
| `1640995200000` | Milliseconds (2022-01-01) | ✅ Convert | `"2022-01-01 00:00:00"` |
| `57414` | Invalid timestamp | ⚠️ Keep original | `57414` |
| `999999999999999` | Out of range | ⚠️ Keep original | `999999999999999` |

**Logging Output:**
```
Timestamp value 57414 for field year doesn't match expected timestamp patterns, keeping original value
Timestamp-millis value 999999999999999 for field timestamp is out of range, keeping original
```

### **Pipeline Testing & Validation**

#### **Comprehensive Test Scripts:**

The system includes comprehensive test scripts to validate the entire processing pipeline before production deployment:

**1. Full Pipeline Test (`test_full_pipeline.py`):**
- Tests actual Avro schema parsing and binary decoding
- Validates timestamp conversion with bounds checking
- Tests field validation against target table schema
- Simulates PostgreSQL operations with real SQL generation
- Includes error scenario testing
- Requires `fastavro` and `psycopg2` dependencies

**2. Standalone Pipeline Test (`test_pipeline_standalone.py`):**
- Complete pipeline simulation without external dependencies
- Uses SQLite for database operation testing
- Processes actual sample data from `pyhub_sample.txt`
- Validates all processing stages independently
- Works in any Python environment

#### **Test Coverage:**

**Processing Stages Tested:**
```bash
✅ Avro union type processing ({"bytes": value}, {"string": value}, etc.)
✅ Timestamp conversion with microsecond/millisecond/second detection
✅ Field validation against target table schema
✅ PostgreSQL value adaptation and type conversion
✅ SQL generation with conflict resolution
✅ Database insertion simulation
✅ Error handling for out-of-range values
✅ Integration with actual postgres_utils functions
```

**Error Scenarios Tested:**
- Out-of-range timestamp values (like the "year 57414" error)
- Invalid decimal formats
- Complex nested data structures
- Very large strings
- Null and empty values
- Mixed timestamp formats
- Debezium CDC format data
- Financial data precision handling

#### **Running the Tests:**

**Standalone Test (Recommended):**
```bash
cd modules/kafka/msk_consumer_refactored
python3 test_pipeline_standalone.py
```

**Full Pipeline Test (with dependencies):**
```bash
cd modules/kafka/msk_consumer_refactored
python3 test_full_pipeline.py
```

#### **Sample Test Output:**
```
🧪 MSK Consumer Pipeline Test (Standalone)
=============================================
📄 Loading sample data...
  ✅ Loaded 79 fields

🔄 Step 1: Processing Avro union types...
  ✅ Processed 79 fields

🔄 Step 2: Converting timestamps...

🔄 Step 3: Validating fields...
  📊 19 matching, 60 extra, 1 missing

🔄 Step 4: Adapting for PostgreSQL...
  ✅ Prepared 20 values

🔄 Step 5: Generating SQL...
  ✅ Generated SQL query (1307 characters)

🔄 Step 6: Testing database simulation...
  ✅ Database test: SUCCESS

🎯 Overall Result: SUCCESS
```

#### **Benefits of Pipeline Testing:**
- **Early Error Detection**: Catch processing issues before production
- **Data Validation**: Verify that sample data processes correctly
- **Performance Insights**: Understand processing behavior with real data
- **Schema Validation**: Ensure field mappings are correct
- **SQL Verification**: Validate generated queries and conflict resolution
- **Regression Testing**: Detect issues when making code changes

#### **Enhanced Avro Union Type Handling:**

The system now properly handles Avro union types commonly found in Kafka messages:

**Supported Union Formats:**
```json
{
  "PYMNT_AMNT": {"bytes": 1000.000000000},           // Decimal amounts
  "PYMNT_CURR": {"string": "USD"},                   // String values
  "PROCESS_DATETIME": {"long": 1750854821000},       // Long integers (timestamps)
  "ORG_BRANCH_CODE_OLD": {"int": 0},                 // Integer values
  "STATUS_DATETIME": {"long": 1750854928435428}      // Microsecond timestamps
}
```

**Processing Logic:**
- **{"bytes": number}**: Converted to `Decimal` for precision
- **{"string": "value"}**: Extracted as string
- **{"long": number}**: Extracted as long integer
- **{"int": number}**: Extracted as integer
- **Microsecond Timestamps**: Automatically detected and converted
- **Out-of-Range Values**: Gracefully handled with warnings

**Benefits:**
- **No Data Loss**: All union types properly extracted
- **Type Safety**: Appropriate Python types for each value
- **Error Resilience**: Graceful handling of malformed data
- **Comprehensive Logging**: Detailed processing information

#### **Configuration File Structure:**
The application expects a JSON configuration file with the following structure:

```json
{
  "table_mapping": {
    "schema.table1": {
      "group-id": "data-platform",
      "topic": "schema.table1",
      "batch_size": 1000,
      "batch_wait_time": 30
    },
    "analytics.events": {
      "group-id": "analytics-group",
      "topic": "analytics.events",
      "batch_size": 500,
      "batch_wait_time": 15
    }
  },
  "kafka_config": {
    "bootstrap.servers": "kafka-broker:9092",
    "security.protocol": "SASL_SSL",
    "sasl.mechanism": "AWS_MSK_IAM"
  },
  "postgres_config": {
    "host": "postgres-host",
    "port": 5432,
    "database": "target_db"
  },
  "schema_registry": {
    "url": "http://schema-registry:8081"
  },
  "log_level": "INFO"
}
```

#### **Configuration Loading Priority:**
1. Command-line arguments (highest priority)
2. Configuration file values
3. Default values (lowest priority)

### Environment Setup

#### **AWS Credentials:**
The application uses AWS Secrets Manager for credential management. Ensure your environment has:

```bash
# AWS credentials configured via:
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-east-1

# Or use IAM roles for EC2/ECS/Lambda
```

#### **Required Secrets in AWS Secrets Manager:**
- **Kafka credentials**: JSON with connection parameters
- **PostgreSQL credentials**: JSON with database connection details

### Process Management

#### **Process Naming:**
Each consumer instance gets a unique process name:
```
Format: kafka_consumer_{table_name}_{group_id}_{instance_num}
Example: kafka_consumer_schema.table1_data-platform_1
```

#### **Multiple Instances:**
- Automatic instance numbering prevents conflicts
- Each instance gets its own log file
- Process names are set for monitoring tools

#### **Log File Management:**
```
logs/
├── kafka_consumer_schema.table1_data-platform_1.log
├── kafka_consumer_schema.table1_data-platform_2.log
├── kafka_consumer_analytics.events_analytics-group_1.log
└── kafka_consumer_orders.transactions_orders-group_1.log
```

### Error Handling & Monitoring

#### **Dead Letter Queue (DLQ):**
- Failed messages are automatically sent to DLQ topics
- DLQ topic naming: `{original_topic}_dlq`
- Automatic topic creation if DLQ doesn't exist

#### **Discarded Messages:**
- **Database logging**: Failed messages stored in `ops.discarded_messages` PostgreSQL table
- **File-based logging**: Detailed error analysis in table-specific files
- **Dual redundancy**: Both database and file logging for maximum reliability
- **Configurable**: Control via global flags in `config_loader.py` (`ENABLE_DISCARDED_MESSAGES_DB` and `ENABLE_DISCARDED_MESSAGES_FILE`)
- **Queryable**: JSONB format enables complex analysis and reporting

#### **Logging Levels:**
- **DEBUG**: Detailed processing information
- **INFO**: Standard operational messages
- **WARNING**: Non-critical issues
- **ERROR**: Critical errors requiring attention

### Performance Configuration

#### **Batch Processing:**
- Configurable batch size per table
- Configurable batch wait time
- Automatic fallback to individual record processing on batch failures

#### **Connection Pooling:**
- PostgreSQL connection pooling for efficiency
- Configurable pool size and connection parameters
- Automatic connection retry logic

#### **Schema Caching:**
- Table metadata caching (columns, primary keys, types)
- Schema registry response caching
- Cache invalidation on errors

---

## 🛠️ Development Guidelines

### Code Organization Principles

#### **Separation of Concerns:**
- Each module has a single, well-defined responsibility
- Database operations are isolated in `postgres_*` modules
- Kafka operations are contained in `kafka_utils.py`
- Configuration management is centralized in `config_loader.py`

#### **Error Handling:**
- Comprehensive exception handling throughout the codebase
- Graceful degradation with fallback mechanisms
- Detailed error logging for debugging
- DLQ integration for message processing failures

#### **Testing Strategy:**
- Unit tests for all modules with comprehensive coverage
- Mock-based testing for external dependencies
- Realistic test data and scenarios
- Automated test runner with proper setup

### Adding New Features

#### **1. Adding New Table Mappings:**
```json
{
  "table_mapping": {
    "new_schema.new_table": {
      "group-id": "new-group",
      "topic": "new_schema.new_table",
      "batch_size": 1000,
      "batch_wait_time": 30
    }
  }
}
```

#### **2. Adding New Data Types:**
- Extend `convert_decimal_fields()` and `convert_date_fields()` in `postgres_utils.py`
- Add corresponding tests in `test_postgres_utils.py`
- Update `adapt_value_for_postgres()` in `postgres_batch_utils.py`

#### **3. Adding New Error Handling:**
- Extend error handling in relevant modules
- Add DLQ integration if needed
- Update logging and monitoring
- Add comprehensive tests

### Testing New Code

#### **1. Create Test File:**
```python
# test_new_module.py
import unittest
from unittest.mock import patch, MagicMock

class TestNewModule(unittest.TestCase):
    def setUp(self):
        # Setup test data and mocks
        pass

    def test_success_scenario(self):
        # Test successful operation
        pass

    def test_error_scenario(self):
        # Test error handling
        pass
```

#### **2. Add to Test Runner:**
Update `run_tests.py` to include the new test module.

#### **3. Run Tests:**
```bash
cd tests
python3 run_tests.py
```

### Best Practices

#### **Code Quality:**
- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Add comprehensive docstrings
- Keep functions focused and small

#### **Error Handling:**
- Always handle exceptions gracefully
- Log errors with sufficient context
- Provide fallback mechanisms where possible
- Use specific exception types

#### **Performance:**
- Use connection pooling for database operations
- Implement caching for frequently accessed data
- Batch operations where possible
- Monitor resource usage

#### **Security:**
- Use AWS Secrets Manager for credentials
- Never hardcode sensitive information
- Validate all input data
- Use secure connection protocols

### Deployment Considerations

#### **Environment Variables:**
```bash
# Required AWS configuration
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_DEFAULT_REGION=us-east-1

# Optional configuration overrides
export LOG_LEVEL=INFO
export BATCH_SIZE=1000
```

#### **Monitoring:**
- Process names are set for system monitoring
- Log files are created per process instance
- Discarded messages are tracked for data quality
- DLQ topics capture failed messages

#### **Scaling:**
- Multiple consumer instances can run simultaneously
- Automatic instance numbering prevents conflicts
- Each instance processes independently
- Load balancing handled by Kafka consumer groups

### Migration Notes

#### **From Original Code:**
- The refactored code is functionally equivalent to the original
- All configuration options and behavior preserved
- Improved modularity allows for easier testing and maintenance
- Better error isolation and debugging capabilities

#### **Configuration Compatibility:**
- Existing configuration files work without changes
- New optional parameters can be added incrementally
- Backward compatibility maintained for all features

### Troubleshooting

#### **Common Issues:**

**Import Errors:**
- Ensure all dependencies are installed
- Check Python path configuration
- Verify module structure

**Connection Failures:**
- Verify AWS credentials and permissions
- Check network connectivity to Kafka and PostgreSQL
- Validate configuration parameters

**Test Failures:**
- Ensure proper mock setup
- Check for test isolation issues
- Verify test data consistency

**Performance Issues:**
- Monitor batch sizes and wait times
- Check connection pool configuration
- Review logging levels (DEBUG can be verbose)

#### **Debug Mode:**
```bash
# Run with debug logging
python3 main.py schema.table1 --log-level DEBUG

# Run specific tests with verbose output
python3 -m unittest tests.test_module_name -v
```

---

## 📋 Summary

This comprehensive documentation covers the complete MSK Consumer architecture and functionality, including:

- ✓ **Modular Architecture**: Clean separation of concerns across multiple modules
- ✓ **Core Functionality**: Dynamic logging, message processing, and error handling
- ✓ **Comprehensive Testing**: Complete test coverage with proper mocking
- ✓ **Detailed Architecture**: Clear module responsibilities and data flow
- ✓ **Usage and Configuration**: Complete deployment and configuration guide
- ✓ **Development Guidelines**: Best practices for future enhancements

The MSK Consumer provides **enterprise-grade reliability, maintainability, and observability** for processing Kafka messages and inserting them into PostgreSQL databases.

**The refactored codebase is production-ready and fully documented.**

---

*Last updated: 2025-06-24*
*Documentation version: 2.0*
