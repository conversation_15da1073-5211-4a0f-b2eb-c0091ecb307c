import json
import logging
import psutil
import setproctitle
import os
from typing import Dict, Any, Optional
from confluent_kafka.schema_registry import SchemaRegistryClient
from secrets_manager import get_kafka_credentials

def load_config(path: str = "./config.json") -> dict:
    """Load configuration from JSON file."""
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"Failed to load config: {e}")
        return {}

def get_local_schema(schema_file: str) -> Optional[Dict]:
    """
    Loads an Avro schema from a local JSON file.
    """
    try:
        with open(schema_file, "r", encoding="utf-8") as f:
            schema = json.load(f)
            logging.info(f"Loaded local schema from {schema_file}")
            return schema
    except Exception as e:
        logging.error(f"Failed to load local schema from {schema_file}: {e}")
        return None

def fetch_schema_from_registry(topic: str) -> Optional[Dict]:
    """
    Fetches and returns the Avro schema from the Confluent Schema Registry
    for a given topic using the Schema Registry client.
    """
    try:
        kafka_credentials = get_kafka_credentials()
        schema_registry_url = kafka_credentials["schema-registry"]

        schema_registry_conf = {'url': schema_registry_url}
        schema_registry_client = SchemaRegistryClient(schema_registry_conf)

        subject_name = f"{topic}-value"
        schema = schema_registry_client.get_latest_version(subject_name)
        schema_str = schema.schema.schema_str

        logging.info(f"Fetched schema from registry for topic {topic}")
        return json.loads(schema_str)
    except Exception as e:
        logging.error(f"Failed to retrieve schema from Schema Registry for topic {topic}: {e}")
        return None

def get_config_value(table_config: Dict, global_config: Dict, key: str, default: Any = None) -> Any:
    """
    Safely retrieve a config value from the table's config if it exists,
    otherwise fall back to the global config, then the provided default.
    """
    return table_config.get(key, global_config.get(key, default))

def set_process_name(table_name: str, config: Dict) -> str:
    """
    Sets a unique process name for the consumer using setproctitle, based on the
    table name and the consumer group ID. Also increments the instance number if
    multiple consumers with the same base name are running.
    """
    table_config = config["table_mapping"][table_name]
    group_id = table_config.get("group-id", "data-platform")
    base_name = f"kafka_consumer_{table_name}_{group_id}"
    instance_num = sum(
        1 for proc in psutil.process_iter(['name'])
        if proc.info['name'] and proc.info['name'].startswith(base_name)
    ) + 1
    process_name = f"{base_name}_{instance_num}"
    setproctitle.setproctitle(process_name)
    logging.info(f"Set process name to: {process_name}")
    logging.info(f"Consuming from topic: {table_config['topic']}")
    return process_name

def setup_logging(process_name: str, log_level: str = "INFO", log_dir: str = "logs") -> None:
    """
    Configure logging to use a file named after the process name.

    Args:
        process_name: The name of the process (used for log filename)
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_dir: Directory to store log files
    """
    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)

    # Create log filename based on process name
    log_filename = f"{log_dir}/{process_name}.log"

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()  # Also log to console
        ]
    )

    logging.info(f"Logging configured for process '{process_name}' -> {log_filename}")

def get_discarded_messages_filename(table_name: str) -> str:
    """
    Generate the discarded messages filename based on the table name.
    Replaces dots with underscores to avoid issues with file paths.
    """
    safe_table_name = table_name.replace('.', '_')
    return f"discarded_messages/{safe_table_name}_discarded_messages.txt"
